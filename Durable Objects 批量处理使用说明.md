# Durable Objects + Alarms 批量处理系统

## 🎯 方案概述

基于 Cloudflare Durable Objects + Alarms 实现的批量账号数据更新系统，支持：

- ✅ **前端一键触发**：管理员点击一次，后台自动处理所有账号
- ✅ **分批顺序处理**：4个账号一批，避免API限制
- ✅ **精确时间控制**：批次间延迟0.5秒，可配置
- ✅ **SSE实时进度**：前端实时显示处理进度
- ✅ **可靠性保证**：Alarms自动重试，状态持久化
- ✅ **错误恢复**：支持暂停、恢复、取消操作

## 🏗️ 技术架构

### 核心组件

1. **BatchProcessorDO** - 批量处理器 Durable Object
   - 管理批处理状态和进度
   - 使用 Alarms 实现定时批次处理
   - 提供 SSE 接口推送实时进度

2. **管理员 API** - 控制接口
   - 启动批量处理
   - 获取处理状态
   - 取消正在进行的处理
   - SSE 进度订阅

3. **前端测试页面** - 可视化控制台
   - 配置批处理参数
   - 实时监控处理进度
   - 查看详细日志

### 处理流程

```
管理员点击 → BatchProcessorDO → 分批处理 → SSE推送进度
     ↓              ↓              ↓           ↓
  启动请求      设置第一个Alarm    处理4个账号   前端更新UI
                     ↓              ↓           ↓
                 0.5秒后触发     更新数据库    显示当前状态
                     ↓              ↓           ↓
                设置下一个Alarm   处理下一批    继续推送进度
                     ↓              ↓           ↓
                  重复直到        所有账号      处理完成
                  全部完成        处理完毕
```

## 🚀 使用方法

### 1. 部署配置

确保 `wrangler.jsonc` 包含以下配置：

```json
{
  "migrations": [
    {
      "new_sqlite_classes": ["BatchProcessorDO"],
      "tag": "v3"
    }
  ],
  "durable_objects": {
    "bindings": [
      {
        "class_name": "BatchProcessorDO",
        "name": "BATCH_PROCESSOR_DO"
      }
    ]
  }
}
```

### 2. API 接口

#### 启动批量处理
```http
POST /api/admin/start-durable-batch-update
Authorization: Bearer YOUR_ADMIN_TOKEN
Content-Type: application/json

{
  "config": {
    "batchSize": 4,              // 每批账号数量
    "delayBetweenBatches": 500,  // 批次间延迟(ms)
    "delayBetweenAccounts": 100  // 账号间延迟(ms)
  }
}
```

#### 获取处理状态
```http
GET /api/admin/batch-status
Authorization: Bearer YOUR_ADMIN_TOKEN
```

#### 取消批量处理
```http
POST /api/admin/cancel-batch-update
Authorization: Bearer YOUR_ADMIN_TOKEN
```

#### SSE 进度订阅
```http
GET /api/admin/batch-progress-sse?batchId=BATCH_ID&token=YOUR_ADMIN_TOKEN
Accept: text/event-stream
```

### 3. 前端集成

使用提供的 `batch-test.html` 测试页面，或集成到现有管理后台：

```javascript
// 启动批量处理
const response = await fetch('/api/admin/start-durable-batch-update', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer YOUR_ADMIN_TOKEN'
  },
  body: JSON.stringify({
    config: {
      batchSize: 4,
      delayBetweenBatches: 500,
      delayBetweenAccounts: 100
    }
  })
});

const result = await response.json();
if (result.success) {
  // 建立 SSE 连接监听进度
  const eventSource = new EventSource(
    `/api/admin/batch-progress-sse?batchId=${result.batchId}&token=YOUR_ADMIN_TOKEN`
  );
  
  eventSource.onmessage = function(event) {
    const data = JSON.parse(event.data);
    if (data.type === 'progress') {
      updateProgressUI(data.data);
    }
  };
}
```

## 📊 进度数据结构

```typescript
interface BatchProgress {
  batchId: string;           // 批处理ID
  totalAccounts: number;     // 总账号数
  processedAccounts: number; // 已处理账号数
  successCount: number;      // 成功数量
  failureCount: number;      // 失败数量
  currentBatch: number;      // 当前批次(从0开始)
  totalBatches: number;      // 总批次数
  status: string;           // 状态: waiting|processing|completed|failed|cancelled
  startTime: number;        // 开始时间戳
  lastUpdateTime: number;   // 最后更新时间戳
  currentAccount?: string;  // 当前处理的账号
  results: FetchResult[];   // 详细结果列表
  errorMessage?: string;    // 错误信息
}
```

## ⚙️ 配置参数

| 参数 | 默认值 | 说明 | 建议范围 |
|------|--------|------|----------|
| `batchSize` | 4 | 每批处理的账号数量 | 1-10 |
| `delayBetweenBatches` | 500ms | 批次间延迟时间 | 100-5000ms |
| `delayBetweenAccounts` | 100ms | 账号间延迟时间 | 50-1000ms |

## 🔧 故障排除

### 常见问题

1. **批量处理无法启动**
   - 检查管理员token是否有效
   - 确认没有其他批处理正在进行
   - 查看控制台错误日志

2. **SSE连接失败**
   - 检查batchId参数是否正确
   - 确认token权限
   - 尝试刷新页面重新连接

3. **处理进度停滞**
   - 检查Durable Object状态
   - 查看Alarm是否正常触发
   - 可以尝试取消后重新启动

### 监控建议

- 监控批处理完成率
- 关注账号处理失败原因
- 定期检查Durable Object健康状态
- 设置处理时间告警

## 🎯 优势对比

| 特性 | 传统方案 | Durable Objects方案 |
|------|----------|-------------------|
| 前端体验 | 需要等待完成 | 一键启动，后台处理 |
| 进度反馈 | 无或有限 | 实时SSE推送 |
| 可靠性 | 依赖单次请求 | Alarms自动重试 |
| 状态管理 | 内存中，易丢失 | 持久化存储 |
| 错误恢复 | 需要重新开始 | 支持暂停恢复 |
| 资源消耗 | 长时间占用连接 | 异步处理，释放连接 |

## 📈 性能特点

- **处理能力**：单个DO软限制1000 req/s，足够批量处理需求
- **延迟控制**：毫秒级精度的Alarms调度
- **状态持久化**：自动保存处理进度，支持故障恢复
- **水平扩展**：可以创建多个BatchProcessorDO实例处理不同任务

## 🔮 扩展可能

1. **多任务并行**：支持同时运行多个不同类型的批处理任务
2. **优先级队列**：重要账号优先处理
3. **智能调度**：根据API响应时间动态调整延迟
4. **结果分析**：批处理完成后的数据分析和报告
5. **WebSocket升级**：支持双向控制操作（暂停/恢复/调整参数）

这个方案完美解决了您的需求：前端发送一次请求，后端即可获取所有账号的全部信息，同时提供了优秀的用户体验和可靠的处理保证。
