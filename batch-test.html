<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Durable Objects 批量处理测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        button {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        .progress-container {
            margin: 20px 0;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
        }
        .progress-fill {
            height: 100%;
            background-color: #007bff;
            transition: width 0.3s ease;
            border-radius: 10px;
        }
        .status-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .status-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #007bff;
        }
        .status-card h4 {
            margin: 0 0 10px 0;
            color: #495057;
        }
        .status-card .value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .log-container {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        .log-info { color: #007bff; }
        .log-success { color: #28a745; }
        .log-error { color: #dc3545; }
        .log-warning { color: #ffc107; }
        .config-section {
            margin-bottom: 20px;
        }
        .config-row {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
        }
        .config-row label {
            min-width: 120px;
            font-weight: bold;
        }
        .config-row input {
            padding: 5px 10px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            width: 100px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Durable Objects + Alarms 批量处理测试</h1>
        <p>测试基于 Durable Objects 和 Alarms 的批量账号数据更新功能，支持 SSE 实时进度推送。</p>
    </div>

    <div class="container">
        <h2>⚙️ 批量处理配置</h2>
        <div class="config-section">
            <div class="config-row">
                <label>每批账号数量:</label>
                <input type="number" id="batchSize" value="4" min="1" max="10">
                <span>个账号/批</span>
            </div>
            <div class="config-row">
                <label>批次间延迟:</label>
                <input type="number" id="delayBetweenBatches" value="500" min="100" max="5000">
                <span>毫秒</span>
            </div>
            <div class="config-row">
                <label>账号间延迟:</label>
                <input type="number" id="delayBetweenAccounts" value="100" min="50" max="1000">
                <span>毫秒</span>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>🎮 控制面板</h2>
        <div class="controls">
            <button id="startBtn" class="btn-primary">启动批量处理</button>
            <button id="statusBtn" class="btn-secondary">获取状态</button>
            <button id="cancelBtn" class="btn-danger" disabled>取消处理</button>
            <button id="clearLogBtn" class="btn-secondary">清空日志</button>
        </div>
    </div>

    <div class="container">
        <h2>📊 处理进度</h2>
        <div class="progress-container">
            <div class="progress-bar">
                <div id="progressFill" class="progress-fill" style="width: 0%"></div>
            </div>
            <div id="progressText">等待开始...</div>
        </div>

        <div class="status-info">
            <div class="status-card">
                <h4>总账号数</h4>
                <div id="totalAccounts" class="value">-</div>
            </div>
            <div class="status-card">
                <h4>已处理</h4>
                <div id="processedAccounts" class="value">-</div>
            </div>
            <div class="status-card">
                <h4>成功</h4>
                <div id="successCount" class="value">-</div>
            </div>
            <div class="status-card">
                <h4>失败</h4>
                <div id="failureCount" class="value">-</div>
            </div>
            <div class="status-card">
                <h4>当前批次</h4>
                <div id="currentBatch" class="value">-</div>
            </div>
            <div class="status-card">
                <h4>状态</h4>
                <div id="status" class="value">-</div>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>📝 实时日志</h2>
        <div id="logContainer" class="log-container">
            <div class="log-entry log-info">等待操作...</div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentBatchId = null;
        let eventSource = null;
        let statusPollingInterval = null;

        // DOM 元素
        const startBtn = document.getElementById('startBtn');
        const statusBtn = document.getElementById('statusBtn');
        const cancelBtn = document.getElementById('cancelBtn');
        const clearLogBtn = document.getElementById('clearLogBtn');
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');
        const logContainer = document.getElementById('logContainer');

        // 配置元素
        const batchSizeInput = document.getElementById('batchSize');
        const delayBetweenBatchesInput = document.getElementById('delayBetweenBatches');
        const delayBetweenAccountsInput = document.getElementById('delayBetweenAccounts');

        // 状态元素
        const totalAccountsEl = document.getElementById('totalAccounts');
        const processedAccountsEl = document.getElementById('processedAccounts');
        const successCountEl = document.getElementById('successCount');
        const failureCountEl = document.getElementById('failureCount');
        const currentBatchEl = document.getElementById('currentBatch');
        const statusEl = document.getElementById('status');

        // 添加日志
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // 更新进度显示
        function updateProgress(progress) {
            if (!progress) {
                progressFill.style.width = '0%';
                progressText.textContent = '等待开始...';
                totalAccountsEl.textContent = '-';
                processedAccountsEl.textContent = '-';
                successCountEl.textContent = '-';
                failureCountEl.textContent = '-';
                currentBatchEl.textContent = '-';
                statusEl.textContent = '-';
                return;
            }

            const percentage = progress.totalAccounts > 0 
                ? Math.round((progress.processedAccounts / progress.totalAccounts) * 100) 
                : 0;
            
            progressFill.style.width = `${percentage}%`;
            progressText.textContent = `${progress.processedAccounts}/${progress.totalAccounts} (${percentage}%)`;
            
            if (progress.currentAccount) {
                progressText.textContent += ` - 正在处理: ${progress.currentAccount}`;
            }

            // 更新状态卡片
            totalAccountsEl.textContent = progress.totalAccounts;
            processedAccountsEl.textContent = progress.processedAccounts;
            successCountEl.textContent = progress.successCount;
            failureCountEl.textContent = progress.failureCount;
            currentBatchEl.textContent = `${progress.currentBatch + 1}/${progress.totalBatches}`;
            statusEl.textContent = progress.status;

            // 根据状态更新按钮
            if (progress.status === 'processing') {
                startBtn.disabled = true;
                cancelBtn.disabled = false;
            } else {
                startBtn.disabled = false;
                cancelBtn.disabled = true;
            }
        }

        // 启动批量处理
        async function startBatchUpdate() {
            try {
                const config = {
                    batchSize: parseInt(batchSizeInput.value),
                    delayBetweenBatches: parseInt(delayBetweenBatchesInput.value),
                    delayBetweenAccounts: parseInt(delayBetweenAccountsInput.value)
                };

                addLog(`启动批量处理，配置: ${JSON.stringify(config)}`);

                const response = await fetch('/api/admin/start-durable-batch-update', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer YOUR_ADMIN_TOKEN' // 需要替换为实际的管理员token
                    },
                    body: JSON.stringify({ config })
                });

                const result = await response.json();

                if (result.success) {
                    currentBatchId = result.batchId;
                    addLog(`批量处理启动成功: ${result.batchId}`, 'success');
                    addLog(`总账号数: ${result.totalAccounts}, 分${result.totalBatches}批处理`);
                    
                    // 开始 SSE 连接监听进度
                    startSSEConnection(currentBatchId);
                    
                    // 开始状态轮询作为备用
                    startStatusPolling();
                } else {
                    addLog(`启动失败: ${result.message}`, 'error');
                }
            } catch (error) {
                addLog(`启动异常: ${error.message}`, 'error');
            }
        }

        // 启动 SSE 连接
        function startSSEConnection(batchId) {
            if (eventSource) {
                eventSource.close();
            }

            addLog(`建立 SSE 连接: ${batchId}`);
            
            eventSource = new EventSource(`/api/admin/batch-progress-sse?batchId=${batchId}&token=YOUR_ADMIN_TOKEN`);
            
            eventSource.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    
                    if (data.type === 'connected') {
                        addLog('SSE 连接已建立', 'success');
                    } else if (data.type === 'progress') {
                        if (data.data) {
                            updateProgress(data.data);
                            addLog(`进度更新: ${data.data.processedAccounts}/${data.data.totalAccounts}`);
                        }
                    }
                } catch (error) {
                    addLog(`SSE 数据解析错误: ${error.message}`, 'error');
                }
            };

            eventSource.onerror = function(event) {
                addLog('SSE 连接错误', 'error');
                eventSource.close();
                eventSource = null;
            };
        }

        // 开始状态轮询
        function startStatusPolling() {
            if (statusPollingInterval) {
                clearInterval(statusPollingInterval);
            }

            statusPollingInterval = setInterval(async () => {
                try {
                    const response = await fetch('/api/admin/batch-status', {
                        headers: {
                            'Authorization': 'Bearer YOUR_ADMIN_TOKEN'
                        }
                    });
                    const result = await response.json();
                    
                    if (result.success && result.data) {
                        updateProgress(result.data);
                        
                        // 如果处理完成，停止轮询
                        if (result.data.status === 'completed' || result.data.status === 'failed' || result.data.status === 'cancelled') {
                            clearInterval(statusPollingInterval);
                            statusPollingInterval = null;
                            
                            if (eventSource) {
                                eventSource.close();
                                eventSource = null;
                            }
                            
                            addLog(`批量处理${result.data.status === 'completed' ? '完成' : result.data.status}`, 
                                   result.data.status === 'completed' ? 'success' : 'warning');
                        }
                    }
                } catch (error) {
                    addLog(`状态轮询错误: ${error.message}`, 'error');
                }
            }, 2000); // 每2秒轮询一次
        }

        // 获取状态
        async function getStatus() {
            try {
                const response = await fetch('/api/admin/batch-status', {
                    headers: {
                        'Authorization': 'Bearer YOUR_ADMIN_TOKEN'
                    }
                });
                const result = await response.json();
                
                if (result.success) {
                    updateProgress(result.data);
                    addLog('状态获取成功', 'success');
                } else {
                    addLog('状态获取失败', 'error');
                }
            } catch (error) {
                addLog(`状态获取异常: ${error.message}`, 'error');
            }
        }

        // 取消批量处理
        async function cancelBatchUpdate() {
            try {
                const response = await fetch('/api/admin/cancel-batch-update', {
                    method: 'POST',
                    headers: {
                        'Authorization': 'Bearer YOUR_ADMIN_TOKEN'
                    }
                });
                const result = await response.json();
                
                if (result.success) {
                    addLog('批量处理已取消', 'warning');
                    
                    // 停止轮询和 SSE
                    if (statusPollingInterval) {
                        clearInterval(statusPollingInterval);
                        statusPollingInterval = null;
                    }
                    
                    if (eventSource) {
                        eventSource.close();
                        eventSource = null;
                    }
                    
                    startBtn.disabled = false;
                    cancelBtn.disabled = true;
                } else {
                    addLog(`取消失败: ${result.message}`, 'error');
                }
            } catch (error) {
                addLog(`取消异常: ${error.message}`, 'error');
            }
        }

        // 清空日志
        function clearLog() {
            logContainer.innerHTML = '<div class="log-entry log-info">日志已清空</div>';
        }

        // 绑定事件
        startBtn.addEventListener('click', startBatchUpdate);
        statusBtn.addEventListener('click', getStatus);
        cancelBtn.addEventListener('click', cancelBatchUpdate);
        clearLogBtn.addEventListener('click', clearLog);

        // 页面加载时获取一次状态
        window.addEventListener('load', () => {
            addLog('页面加载完成，可以开始测试', 'success');
            getStatus();
        });

        // 页面卸载时清理资源
        window.addEventListener('beforeunload', () => {
            if (eventSource) {
                eventSource.close();
            }
            if (statusPollingInterval) {
                clearInterval(statusPollingInterval);
            }
        });
    </script>
</body>
</html>
