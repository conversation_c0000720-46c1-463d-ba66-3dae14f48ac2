<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>后台管理系统</title>
  <link rel="stylesheet" href="styles.css">
</head>
<body class="admin-body">
  <div id="login-container" class="container login-container-wrapper">
    <div class="card admin-login-box">
      <h2>管理员登录</h2>
      <div class="form-group">
        <label for="username">用户名:</label>
        <input type="text" id="username" class="form-control">
      </div>
      <div class="form-group">
        <label for="password">密码:</label>
        <input type="password" id="password" class="form-control">
      </div>
      <button class="button button-primary button-full-width" id="login-btn">登录</button>
      <div id="admin-login-message" class="message hidden"></div>
    </div>
  </div>

  <div id="admin-container" class="container admin-main-container hidden">
    <header class="admin-header">
      <h1>后台管理系统</h1>
      <!-- Można dodać przycisk wylogowania administratora -->
      <!-- <button id="admin-logout-btn" class="button button-secondary">退出</button> -->
      <button id="change-password-btn" class="button button-primary">修改密码</button>
    </header>

    <div class="tabs admin-tabs">
      <div class="tab active" id="accounts-tab">账号管理</div>
      <div class="tab" id="account-info-tab">账号信息</div>
      <div class="tab" id="activation-codes-tab">激活码管理</div>
      <div class="tab" id="activation-records-tab">激活记录</div>
      <div class="tab" id="version-tab">版本管理</div>
    </div>

    <div id="message" class="message hidden admin-message-area"></div>

    <div id="accounts-panel" class="tab-panel">
      <div class="panel-controls">
        <input type="text" id="search-input" placeholder="输入手机号搜索..." class="form-control">
        <button class="button button-primary" id="search-btn">搜索</button>
        <button class="button button-secondary" id="refresh-all-users-btn">刷新全部状态</button>
      </div>

      <div class="table-container">
        <table>
          <thead>
            <tr>
              <th class="phone-column">手机号</th>
              <th class="status-column">账号类型</th>
              <th class="phone-column">主账号</th>
              <th class="date-column">注册时间</th>
              <th class="date-column">最后登录</th>
              <th class="status-column">在线状态</th>
              <th class="status-column">激活状态</th>
              <th class="date-column">到期时间</th>
              <th class="action-column">操作</th>
            </tr>
          </thead>
          <tbody id="accounts-table-body">
            <!-- Data will be dynamically populated -->
          </tbody>
        </table>
      </div>
    </div>

    <div id="account-info-panel" class="tab-panel hidden">
      <div class="panel-controls">
        <input type="text" id="account-info-search-input" placeholder="输入平台手机号搜索..." class="form-control">
        <button class="button button-primary" id="account-info-search-btn">搜索</button>
        <button class="button button-success" id="add-account-info-btn">添加平台账号</button>

        <!-- 一键更新功能组 -->
        <div class="update-buttons-group">
          <button class="button button-primary" id="durable-batch-update-btn">🚀 一键更新全部信息 (DO+Alarms)</button>
          <button class="button button-secondary" id="batch-status-btn">📊 查看批量处理状态</button>
          <button class="button button-danger" id="cancel-batch-btn" disabled>❌ 取消批量处理</button>
          <button class="button button-warning" id="reset-batch-btn">🔄 重置批量处理状态</button>
          <button class="button button-info" id="manual-update-income-btn">一键更新收益</button>
          <button class="button button-info" id="manual-update-credit-fans-btn">一键更新信用分和粉丝数</button>
          <button class="button button-info" id="manual-update-account-info-btn">一键更新账号详细信息</button>
          <button class="button button-info" id="manual-update-account-status-btn">一键更新账号状态</button>
          <button class="button button-info" id="manual-update-reading-data-btn">一键更新阅读量</button>
          <button class="button button-info" id="manual-update-drafts-count-btn">一键更新草稿箱数量</button>
        </div>

        <!-- 批量处理进度显示 -->
        <div id="batch-progress-container" class="batch-progress-container hidden">
          <h4>📈 批量处理进度</h4>
          <div class="progress-bar-container">
            <div class="progress-bar">
              <div id="batch-progress-fill" class="progress-fill" style="width: 0%"></div>
            </div>
            <div id="batch-progress-text" class="progress-text">等待开始...</div>
          </div>
          <div class="batch-stats">
            <span id="batch-total-accounts" class="stat-item">总账号: -</span>
            <span id="batch-processed-accounts" class="stat-item">已处理: -</span>
            <span id="batch-success-count" class="stat-item">成功: -</span>
            <span id="batch-failure-count" class="stat-item">失败: -</span>
            <span id="batch-current-batch" class="stat-item">当前批次: -</span>
            <span id="batch-status" class="stat-item">状态: -</span>
          </div>
          <div id="batch-current-account" class="current-account">当前处理: -</div>
        </div>

        <!-- 测试功能组 -->
        <div class="test-buttons-group">
          <button class="button button-secondary" id="test-scheduled-task-btn">测试定时任务</button>
          <button class="button button-warning" id="test-api-pressure-btn">API压力测试</button>
          <button class="button button-danger" id="test-reset-btn">测试重置昨日数据</button>
        </div>
      </div>

      <div class="table-container">
        <table>
          <thead>
            <tr>
              <th class="phone-column">所属账号</th>
              <th class="phone-column">平台手机号</th>
              <th class="platform-column">平台</th>
              <th class="username-column">用户名</th>
              <th class="login-type-column">内容类型</th>
              <th class="username-column">团队标签</th>
              <th class="status-column">认证状态</th>
              <th class="status-column">账户状态</th>
              <th class="stats-column">粉丝数</th>
              <th class="stats-column">信用分</th>
              <th class="stats-column">总收益</th>
              <th class="stats-column">昨日收益</th>
              <th class="stats-column">可提现收益</th>
              <th class="stats-column">总阅读</th>
              <th class="stats-column">昨日阅读</th>
              <th class="stats-column">草稿箱</th>
              <th class="action-column">操作</th>
            </tr>
          </thead>
          <tbody id="account-info-table-body">
            <!-- Data will be dynamically populated -->
          </tbody>
        </table>
      </div>
    </div>

    <div id="activation-codes-panel" class="tab-panel hidden">
      <div class="panel-controls filter-bar">
        <select id="code-type-filter" class="form-control">
          <option value="">全部类型</option>
          <option value="1">1天</option>
          <option value="7">7天</option>
          <option value="30">30天</option>
          <option value="90">90天</option>
          <option value="365">365天</option>
        </select>
        <select id="code-status-filter" class="form-control">
          <option value="">全部状态</option>
          <option value="false">未使用</option>
          <option value="true">已使用</option>
        </select>
        <button class="button button-primary" id="filter-codes-btn">筛选</button>
      </div>

      <div class="card generation-card">
        <h3>生成激活码</h3>
        <div class="form-group">
          <label for="new-code-type">类型:</label>
          <select id="new-code-type" class="form-control">
            <option value="1">1天</option>
            <option value="7">7天</option>
            <option value="30">30天</option>
            <option value="90">90天</option>
            <option value="365">365天</option>
          </select>
        </div>
        <div class="form-group">
          <label for="new-code-count">数量:</label>
          <input type="number" id="new-code-count" class="form-control" value="1" min="1" max="100">
        </div>
        <div class="form-actions">
          <button class="button button-primary" id="generate-codes-btn">生成激活码</button>
        </div>
      </div>

      <div id="generated-codes" class="code-box hidden"></div>

      <div class="table-container">
        <table>
          <thead>
            <tr>
              <th class="platform-column">激活码</th>
              <th class="status-column">类型</th>
              <th class="date-column">创建时间</th>
              <th class="status-column">状态</th>
              <th class="date-column">使用时间</th>
              <th class="phone-column">使用者手机号</th>
              <th class="action-column">操作</th>
            </tr>
          </thead>
          <tbody id="codes-table-body">
            <!-- Data will be dynamically populated -->
          </tbody>
        </table>
      </div>
    </div>

    <div id="activation-records-panel" class="tab-panel hidden">
      <div class="table-container">
        <table>
          <thead>
            <tr>
              <th class="platform-column">激活码</th>
              <th class="date-column">使用时间</th>
              <th class="phone-column">使用手机号</th>
              <th class="stats-column">天数</th>
            </tr>
          </thead>
          <tbody id="records-table-body">
            <!-- Data will be dynamically populated -->
          </tbody>
        </table>
      </div>
    </div>

    <div id="version-panel" class="tab-panel hidden">
      <div class="card">
        <h3>客户端版本管理</h3>
        <div class="form-group">
          <label for="current-version-display">当前版本:</label>
          <input type="text" id="current-version-display" class="form-control" readonly>
        </div>
        <div class="form-group">
          <label for="new-version">设置新版本:</label>
          <input type="text" id="new-version" class="form-control" placeholder="例如: 1.0.1">
        </div>
        <div class="form-actions">
          <button class="button button-primary" id="set-version-btn">更新版本</button>
        </div>
      </div>
    </div>
  </div>

  <!-- 修改密码对话框 -->
  <div id="change-password-modal" class="modal hidden">
    <div class="modal-content">
      <span class="close-modal">&times;</span>
      <h2>修改管理员密码</h2>
      <div class="form-group">
        <label for="old-password">旧密码:</label>
        <input type="password" id="old-password" class="form-control">
      </div>
      <div class="form-group">
        <label for="new-password">新密码:</label>
        <input type="password" id="new-password" class="form-control">
      </div>
      <div class="form-group">
        <label for="confirm-password">确认新密码:</label>
        <input type="password" id="confirm-password" class="form-control">
      </div>
      <button class="button button-primary button-full-width" id="submit-password-btn">确认修改</button>
      <div id="password-change-message" class="message hidden"></div>
    </div>
  </div>

  <!-- 平台账号信息模态框 -->
  <div id="account-info-modal" class="modal hidden">
    <div class="modal-content">
      <span class="close-account-info-modal">&times;</span>
      <h2 id="account-info-modal-title">添加平台账号信息</h2>

      <div class="form-group">
        <label for="account-json-data">平台账号JSON数据:</label>
        <textarea id="account-json-data" class="form-control" rows="20" placeholder="请输入完整的平台账号JSON数据，格式如下：
{
  &quot;phone&quot;: &quot;***********&quot;,
  &quot;platform&quot;: &quot;头条号&quot;,
  &quot;login_type&quot;: &quot;视频&quot;,
  &quot;team_tag&quot;: &quot;&quot;,
  &quot;data_update_time&quot;: &quot;2025-07-01 10:02:13&quot;,
  &quot;login_time&quot;: &quot;2025-06-30 15:21:10&quot;,
  &quot;username&quot;: &quot;你的梦&quot;,
  &quot;sessionid&quot;: &quot;e25729e8fcb993cae21f9375efa71d36&quot;,
  &quot;homepage_url&quot;: &quot;https://www.toutiao.com/c/user/****************/&quot;,
  &quot;is_verified&quot;: &quot;否&quot;,
  &quot;drafts_count&quot;: &quot;-&quot;,
  &quot;stats&quot;: {
    &quot;followers&quot;: &quot;0&quot;,
    &quot;total_reads&quot;: &quot;2&quot;,
    &quot;total_income&quot;: &quot;66.47&quot;,
    &quot;yesterday_reads&quot;: &quot;计算中&quot;,
    &quot;yesterday_income&quot;: &quot;3.71&quot;,
    &quot;credit_score&quot;: &quot;100&quot;,
    &quot;can_withdraw_amount&quot;: &quot;0.72&quot;
  },
  &quot;account_status&quot;: &quot;正常&quot;
}"></textarea>
      </div>
      <button class="button button-primary button-full-width" id="submit-account-info-btn">确认</button>
      <div id="account-info-message" class="message hidden"></div>
    </div>
  </div>

  <!-- 转移平台账号模态框 -->
  <div id="transfer-modal" class="modal hidden">
    <div class="modal-content">
      <span class="close-transfer-modal">&times;</span>
      <h2>转移平台账号</h2>
      <div class="form-group">
        <label for="transfer-new-holder-select">选择新归属用户:</label>
        <select id="transfer-new-holder-select" class="form-control">
          <option value="">请选择用户...</option>
        </select>
      </div>
      <button class="button button-primary button-full-width" id="confirm-transfer-btn">确认转移</button>
      <div id="transfer-message" class="message hidden"></div>
    </div>
  </div>

  <!-- 定时任务测试模态框 -->
  <div id="scheduled-task-modal" class="modal hidden">
    <div class="modal-content" style="max-width: 900px; width: 95%;">
      <span class="close-scheduled-task-modal">&times;</span>
      <h2>定时任务测试</h2>

      <div class="form-group">
        <label for="task-delay-input">延迟时间（秒）:</label>
        <input type="number" id="task-delay-input" class="form-control" value="5" min="1" max="300" placeholder="输入1-300秒">
        <small>设置多少秒后执行定时任务（模拟定时触发）</small>
      </div>

      <div class="form-group">
        <button class="button button-primary" id="start-scheduled-test-btn">开始测试</button>
        <button class="button button-danger" id="stop-scheduled-test-btn" disabled>停止测试</button>
      </div>

      <div class="form-group">
        <label>任务状态:</label>
        <div id="task-status" class="task-status">未开始</div>
      </div>

      <div class="form-group">
        <label>实时日志:</label>
        <div id="task-logs" class="task-logs"></div>
        <button class="button button-secondary button-small" id="clear-logs-btn">清空日志</button>
      </div>

      <div id="scheduled-task-message" class="message hidden"></div>
    </div>
  </div>

  <!-- API压力测试模态框 -->
  <div id="api-pressure-modal" class="modal hidden">
    <div class="modal-content" style="max-width: 1000px; width: 95%;">
      <span class="close-api-pressure-modal">&times;</span>
      <h2>API压力测试</h2>

      <div class="form-row">
        <div class="form-group">
          <label for="test-sessionid-input">测试SessionID:</label>
          <input type="text" id="test-sessionid-input" class="form-control" placeholder="输入有效的sessionid进行测试">
        </div>

        <div class="form-group">
          <label for="test-count-input">测试数量:</label>
          <input type="number" id="test-count-input" class="form-control" value="10" min="1" max="1000" placeholder="1-1000">
        </div>

        <div class="form-group">
          <label for="test-interval-input">请求间隔(ms):</label>
          <input type="number" id="test-interval-input" class="form-control" value="1000" min="0" max="10000" placeholder="0-10000毫秒">
        </div>
      </div>

      <div class="form-group">
        <button class="button button-primary" id="start-pressure-test-btn">开始压力测试</button>
        <button class="button button-danger" id="stop-pressure-test-btn" disabled>停止测试</button>
        <button class="button button-secondary" id="clear-pressure-logs-btn">清空日志</button>
      </div>

      <div class="test-stats">
        <div class="stat-item">
          <label>总请求数:</label>
          <span id="total-requests">0</span>
        </div>
        <div class="stat-item">
          <label>成功数:</label>
          <span id="success-requests" class="success-count">0</span>
        </div>
        <div class="stat-item">
          <label>失败数:</label>
          <span id="failed-requests" class="error-count">0</span>
        </div>
        <div class="stat-item">
          <label>成功率:</label>
          <span id="success-rate">0%</span>
        </div>
        <div class="stat-item">
          <label>平均响应时间:</label>
          <span id="avg-response-time">0ms</span>
        </div>
        <div class="stat-item">
          <label>测试状态:</label>
          <span id="pressure-test-status" class="test-status">未开始</span>
        </div>
      </div>

      <div class="form-group">
        <label>详细日志:</label>
        <div id="pressure-test-logs" class="pressure-test-logs"></div>
      </div>

      <div id="api-pressure-message" class="message hidden"></div>
    </div>
  </div>

  <script>
    // API基本URL
    const API_URL = window.location.origin;

    // Login related DOM elements
    const loginContainer = document.getElementById('login-container');
    const adminContainer = document.getElementById('admin-container');
    const loginBtn = document.getElementById('login-btn');
    const usernameInput = document.getElementById('username');
    const passwordInput = document.getElementById('password');
    const adminLoginMessageDiv = document.getElementById('admin-login-message');

    // Admin panel DOM elements
    const accountsTab = document.getElementById('accounts-tab');
    const accountInfoTab = document.getElementById('account-info-tab');
    const activationCodesTab = document.getElementById('activation-codes-tab');
    const activationRecordsTab = document.getElementById('activation-records-tab');
    const versionTab = document.getElementById('version-tab');

    const accountsPanel = document.getElementById('accounts-panel');
    const accountInfoPanel = document.getElementById('account-info-panel');
    const activationCodesPanel = document.getElementById('activation-codes-panel');
    const activationRecordsPanel = document.getElementById('activation-records-panel');
    const versionPanel = document.getElementById('version-panel');

    const searchInput = document.getElementById('search-input');
    const searchBtn = document.getElementById('search-btn');
    const accountsTableBody = document.getElementById('accounts-table-body');
    const codesTableBody = document.getElementById('codes-table-body');
    const recordsTableBody = document.getElementById('records-table-body');
    const messageDiv = document.getElementById('message'); // General message for admin panel

    const codeTypeFilter = document.getElementById('code-type-filter');
    const codeStatusFilter = document.getElementById('code-status-filter');
    const filterCodesBtn = document.getElementById('filter-codes-btn');

    const newCodeType = document.getElementById('new-code-type');
    const newCodeCount = document.getElementById('new-code-count');
    const generateCodesBtn = document.getElementById('generate-codes-btn');
    const generatedCodes = document.getElementById('generated-codes');

    const refreshAllUsersBtn = document.getElementById('refresh-all-users-btn');
    const changePasswordBtn = document.getElementById('change-password-btn');
    const changePasswordModal = document.getElementById('change-password-modal');
    const closeModalBtn = document.querySelector('.close-modal');
    const submitPasswordBtn = document.getElementById('submit-password-btn');
    const oldPasswordInput = document.getElementById('old-password');
    const newPasswordInput = document.getElementById('new-password');
    const confirmPasswordInput = document.getElementById('confirm-password');
    const passwordChangeMessage = document.getElementById('password-change-message');

    const currentVersionDisplay = document.getElementById('current-version-display');
    const newVersionInput = document.getElementById('new-version');
    const setVersionBtn = document.getElementById('set-version-btn');

    // 账号信息相关DOM元素
    const accountInfoSearchInput = document.getElementById('account-info-search-input');
    const accountInfoSearchBtn = document.getElementById('account-info-search-btn');
    const addAccountInfoBtn = document.getElementById('add-account-info-btn');
    const accountInfoTableBody = document.getElementById('account-info-table-body');
    const accountInfoModal = document.getElementById('account-info-modal');
    const closeAccountInfoModalBtn = document.querySelector('.close-account-info-modal');
    const submitAccountInfoBtn = document.getElementById('submit-account-info-btn');
    const accountInfoModalTitle = document.getElementById('account-info-modal-title');
    const accountInfoMessage = document.getElementById('account-info-message');

    // 账号信息表单元素
    const accountJsonDataInput = document.getElementById('account-json-data');

    // 转移相关DOM元素
    const transferModal = document.getElementById('transfer-modal');
    const closeTransferModalBtn = document.querySelector('.close-transfer-modal');
    const confirmTransferBtn = document.getElementById('confirm-transfer-btn');
    const transferNewHolderSelect = document.getElementById('transfer-new-holder-select');
    const transferMessage = document.getElementById('transfer-message');

    // 定时任务测试相关DOM元素
    const scheduledTaskModal = document.getElementById('scheduled-task-modal');
    const closeScheduledTaskModalBtn = document.querySelector('.close-scheduled-task-modal');
    const taskDelayInput = document.getElementById('task-delay-input');
    const startScheduledTestBtn = document.getElementById('start-scheduled-test-btn');
    const stopScheduledTestBtn = document.getElementById('stop-scheduled-test-btn');
    const taskStatus = document.getElementById('task-status');
    const taskLogs = document.getElementById('task-logs');
    const clearLogsBtn = document.getElementById('clear-logs-btn');
    const scheduledTaskMessage = document.getElementById('scheduled-task-message');

    // API压力测试相关DOM元素
    const apiPressureModal = document.getElementById('api-pressure-modal');
    const closeApiPressureModalBtn = document.querySelector('.close-api-pressure-modal');
    const testSessionidInput = document.getElementById('test-sessionid-input');
    const testCountInput = document.getElementById('test-count-input');
    const testIntervalInput = document.getElementById('test-interval-input');
    const startPressureTestBtn = document.getElementById('start-pressure-test-btn');
    const stopPressureTestBtn = document.getElementById('stop-pressure-test-btn');
    const clearPressureLogsBtn = document.getElementById('clear-pressure-logs-btn');
    const totalRequestsSpan = document.getElementById('total-requests');
    const successRequestsSpan = document.getElementById('success-requests');
    const failedRequestsSpan = document.getElementById('failed-requests');
    const successRateSpan = document.getElementById('success-rate');
    const avgResponseTimeSpan = document.getElementById('avg-response-time');
    const pressureTestStatusSpan = document.getElementById('pressure-test-status');
    const pressureTestLogs = document.getElementById('pressure-test-logs');
    const apiPressureMessage = document.getElementById('api-pressure-message');

    let currentEditingAccountId = null;
    let currentTransferAccountId = null;
    let scheduledTaskTimer = null;
    let taskLogInterval = null;
    let pressureTestTimer = null;
    let pressureTestRunning = false;
    let currentTransferMainAccountId = null;
    let adminSSE = null; // 管理员SSE连接

    // Show message function (general for admin panel)
    function showAdminMessage(text, type = 'info', targetDiv = messageDiv) {
        targetDiv.textContent = text;
        targetDiv.className = `message message-${type}`;
        targetDiv.classList.remove('hidden');

        setTimeout(() => {
            targetDiv.classList.add('hidden');
        }, 4000);
    }


    function checkLoginStatus() {
        const token = getAdminToken();

        if (token) {
            // 有token，尝试验证其有效性

            // 通过调用一个简单的API来验证token
            fetch(`${API_URL}/api/admin/accounts`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                }
            }).then(response => {
                if (response.ok) {
                    // Token有效，直接显示管理界面
                    loginContainer.classList.add('hidden');
                    adminContainer.classList.remove('hidden');
                    loadAccounts();
                    connectAdminSSE();
                } else {
                    // Token无效，清除并显示登录界面
                    clearAdminToken();
                    loginContainer.classList.remove('hidden');
                    adminContainer.classList.add('hidden');
                    adminLoginMessageDiv.classList.add('hidden');
                }
            }).catch(error => {
                console.error('验证管理员token时出错:', error);
                clearAdminToken();
                loginContainer.classList.remove('hidden');
                adminContainer.classList.add('hidden');
                adminLoginMessageDiv.classList.add('hidden');
            });
        } else {
            // 没有token，显示登录界面
            loginContainer.classList.remove('hidden');
            adminContainer.classList.add('hidden');
            adminLoginMessageDiv.classList.add('hidden');
        }
    }

    loginBtn.addEventListener('click', async () => {
        const username = usernameInput.value.trim();
        const password = passwordInput.value.trim();

        if (!username || !password) {
            showAdminMessage('请输入用户名和密码', 'error', adminLoginMessageDiv);
            return;
        }

        try {
            const response = await fetch(`${API_URL}/api/admin/login`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ username, password })
            });
            const data = await response.json();

            if (data.success) {
                // 保存管理员token
                if (data.token) {
                    setAdminToken(data.token);
                }

                showAdminMessage('登录成功!', 'success', adminLoginMessageDiv);
                setTimeout(() => { // Give time for user to see success message
                    loginContainer.classList.add('hidden');
                    adminContainer.classList.remove('hidden');
                    adminLoginMessageDiv.classList.add('hidden'); // Hide login message
                    loadAccounts(); // Load initial data for the default tab
                    connectAdminSSE(); // 建立SSE连接
                }, 1000);
            } else {
                showAdminMessage(data.message || '登录失败，请检查凭据。', 'error', adminLoginMessageDiv);
            }
        } catch (error) {
            console.error('登录请求失败:', error);
            showAdminMessage('登录请求失败，请稍后重试。', 'error', adminLoginMessageDiv);
        }
    });

    // 初始化标签页状态
    function initializeTabs() {
        // 默认显示账号管理标签页
        switchTab(accountsTab, accountsPanel);
        loadAccounts();
    }

    document.addEventListener('DOMContentLoaded', () => {
        checkLoginStatus();
        initializeTabs();
    });

    function switchTab(activeTab, activePanel) {
        // Tabs - 包含所有标签页
        [accountsTab, accountInfoTab, activationCodesTab, activationRecordsTab, versionTab].forEach(tab => tab.classList.remove('active'));
        activeTab.classList.add('active');

        // Panels - 包含所有面板
        [accountsPanel, accountInfoPanel, activationCodesPanel, activationRecordsPanel, versionPanel].forEach(panel => {
            panel.classList.add('hidden');
            panel.style.display = 'none';
        });

        // 显示当前激活的面板
        activePanel.classList.remove('hidden');
        activePanel.style.display = 'block';

        // Clear general messages when switching tabs
        messageDiv.classList.add('hidden');
    }

    accountsTab.addEventListener('click', () => {
        switchTab(accountsTab, accountsPanel);
        loadAccounts();
    });

    accountInfoTab.addEventListener('click', () => {
        switchTab(accountInfoTab, accountInfoPanel);
        loadAccountInfoList();
    });

    activationCodesTab.addEventListener('click', () => {
        switchTab(activationCodesTab, activationCodesPanel);
        loadActivationCodes();
    });

    activationRecordsTab.addEventListener('click', () => {
        switchTab(activationRecordsTab, activationRecordsPanel);
        loadActivationRecords();
    });

    versionTab.addEventListener('click', () => {
        switchTab(versionTab, versionPanel);
        loadClientVersion();
    });


    // 管理员token管理
    let adminToken = localStorage.getItem('admin_token');

    function setAdminToken(token) {
        adminToken = token;
        if (token) {
            localStorage.setItem('admin_token', token);
        } else {
            localStorage.removeItem('admin_token');
        }
    }

    function getAdminToken() {
        if (!adminToken) {
            adminToken = localStorage.getItem('admin_token');
        }
        return adminToken;
    }

    function clearAdminToken() {
        adminToken = null;
        localStorage.removeItem('admin_token');
    }

    async function fetchWithAuth(url, options = {}) {
        const token = getAdminToken();

        if (!token) {
            showAdminMessage('未找到管理员token，请重新登录。', 'error');
            checkLoginStatus();
            throw new Error('No admin token');
        }

        // 添加token到请求中
        const headers = {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
            ...options.headers
        };

        const response = await fetch(url, {
            ...options,
            headers
        });

        if (response.status === 401 || response.status === 403) {
            showAdminMessage('管理员token无效或已过期，请重新登录。', 'error');
            clearAdminToken();
            checkLoginStatus();
            throw new Error('Unauthorized');
        }

        return response.json();
    }


    // 建立管理员SSE连接
    function connectAdminSSE() {
      if (adminSSE && adminSSE.readyState === EventSource.OPEN) {
        return; // 已经连接
      }

      const token = getAdminToken();
      if (!token) {
        console.error('无法建立管理员SSE连接：缺少token');
        return;
      }

      const sseUrl = `${window.location.origin}/api/auth/admin/sse?token=${encodeURIComponent(token)}`;

      adminSSE = new EventSource(sseUrl);

      adminSSE.onopen = function() {
        // 连接建立后立即刷新所有用户状态
        setTimeout(() => {
          autoRefreshAllActiveUsers();
        }, 1000); // 延迟1秒确保连接稳定
      };

      adminSSE.onmessage = function(event) {
        try {
          const message = JSON.parse(event.data);

          if (message.type === 'user_status_change') {
            handleUserStatusChange(message.data);
          } else if (message.type === 'connected') {
            // SSE连接确认，无需处理
          } else if (message.type === 'platform_account_notification') {
            handlePlatformAccountNotification(message);
          } else if (message.type === 'platform_account_deleted_notification') {
            handlePlatformAccountDeletedNotification(message);
          } else if (message.type === 'platform_account_transferred_notification') {
            handlePlatformAccountTransferredNotification(message);
          }
        } catch (error) {
          console.error('处理SSE消息失败:', error);
        }
      };

      adminSSE.onerror = function(error) {
        console.error('管理员SSE连接错误:', error);
        // EventSource会自动重连，无需手动处理
      };
    }

    // 处理用户状态变化
    function handleUserStatusChange(data) {
      const { userId, phone, isOnline } = data;

      // 更新表格中对应用户的在线状态
      const rows = accountsTableBody.querySelectorAll('tr');
      for (const row of rows) {
        const phoneCell = row.cells[0]; // 手机号在第一列
        if (phoneCell && phoneCell.textContent === phone) {
          const statusCell = row.cells[5]; // 在线状态在第六列
          if (statusCell) {
            const statusBadge = statusCell.querySelector('.status-badge');
            if (statusBadge) {
              statusBadge.className = `status-badge ${isOnline ? 'status-online' : 'status-offline'}`;
              statusBadge.textContent = isOnline ? '在线' : '离线';
            }
          }

          // 更新操作列的强制下线按钮
          const actionCell = row.cells[8]; // 操作列在第九列
          if (actionCell) {
            const existingForceLogoutBtn = actionCell.querySelector('.button-warning');
            if (isOnline && !existingForceLogoutBtn) {
              // 添加强制下线按钮
              const forceLogoutBtn = document.createElement('button');
              forceLogoutBtn.className = 'button button-warning button-small';
              forceLogoutBtn.textContent = '强制下线';
              forceLogoutBtn.onclick = () => forceLogout(phone);
              actionCell.appendChild(forceLogoutBtn);
            } else if (!isOnline && existingForceLogoutBtn) {
              // 移除强制下线按钮
              existingForceLogoutBtn.remove();
            }
          }
          break;
        }
      }
    }

    // 处理平台账号添加通知
    function handlePlatformAccountNotification(message) {

      // 显示通知消息
      if (message.message) {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = 'alert alert-info alert-dismissible fade show';
        notification.style.cssText = `
          position: fixed;
          top: 20px;
          right: 20px;
          z-index: 9999;
          max-width: 400px;
          box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        `;

        notification.innerHTML = `
          <div class="d-flex align-items-start">
            <i class="fas fa-plus-circle text-info me-2 mt-1"></i>
            <div class="flex-grow-1">
              <strong>平台账号添加</strong><br>
              <small>${message.message}</small>
              <div class="text-muted small mt-1">${new Date().toLocaleTimeString()}</div>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
          </div>
        `;

        document.body.appendChild(notification);

        // 5秒后自动移除通知
        setTimeout(() => {
          if (notification.parentNode) {
            notification.remove();
          }
        }, 5000);
      }

      // 如果当前在平台账号管理页面，刷新数据
      const accountInfoPanel = document.querySelector('#account-info-panel');

      if (accountInfoPanel && !accountInfoPanel.classList.contains('hidden')) {
        // 延迟刷新，确保数据库已更新
        setTimeout(() => {
          if (typeof loadAccountInfoList === 'function') {
            loadAccountInfoList().catch(error => {
              console.error('数据刷新失败:', error);
            });
          }
        }, 1000);
      }
    }

    // 处理平台账号删除通知
    function handlePlatformAccountDeletedNotification(message) {

      // 显示通知弹窗
      showAdminMessage(message.message, 'warning');

      // 如果当前在平台账号管理页面，刷新数据
      const accountInfoPanel = document.querySelector('#account-info-panel');

      if (accountInfoPanel && !accountInfoPanel.classList.contains('hidden')) {
        // 延迟刷新，确保数据库已更新
        setTimeout(() => {
          if (typeof loadAccountInfoList === 'function') {
            loadAccountInfoList().catch(error => {
              console.error('数据刷新失败:', error);
            });
          }
        }, 1000);
      }
    }

    // 处理平台账号转移通知
    function handlePlatformAccountTransferredNotification(message) {
      // 显示通知弹窗
      showAdminMessage(message.message, 'info');

      // 如果当前在平台账号管理页面，刷新数据
      const accountInfoPanel = document.querySelector('#account-info-panel');

      if (accountInfoPanel && !accountInfoPanel.classList.contains('hidden')) {
        // 延迟刷新，确保数据库已更新
        setTimeout(() => {
          if (typeof loadAccountInfoList === 'function') {
            loadAccountInfoList().catch(error => {
              console.error('数据刷新失败:', error);
            });
          }
        }, 1000);
      }
    }



    async function loadAccounts(searchPhone = '') {
        try {
            const url = searchPhone ? `${API_URL}/api/admin/accounts?phone=${encodeURIComponent(searchPhone)}` : `${API_URL}/api/admin/accounts`;
            const data = await fetchWithAuth(url);

            if (data.success) {
                if (data.accounts.length === 0 && searchPhone) {
                     accountsTableBody.innerHTML = `<tr><td colspan="9" class="text-center">未找到手机号 "${searchPhone}" 相关的账号。</td></tr>`;
                     return;
                }
                if (data.accounts.length === 0) {
                    accountsTableBody.innerHTML = `<tr><td colspan="9" class="text-center">暂无账号数据。</td></tr>`;
                    return;
                }

                // 在线状态现在直接从服务器返回，无需额外检查

                accountsTableBody.innerHTML = data.accounts.map(account => {
                    const hasExpiry = account.expiryDate && new Date(account.expiryDate) > new Date();
                    const activationStatus = hasExpiry ? '已激活' : '未激活';
                    const expiryDateStr = account.expiryDate ? new Date(account.expiryDate).toLocaleString() : 'N/A';
                    const registerTimeStr = account.registerTime ? new Date(account.registerTime).toLocaleString() : 'N/A';
                    const lastLoginTimeStr = account.lastLoginTime ? new Date(account.lastLoginTime).toLocaleString() : '从未登录';

                    // 账号类型和主账号信息
                    const accountType = account.accountType || '主账号';
                    const isSubAccount = accountType === '子账号';
                    const mainAccountInfo = isSubAccount && account.ownerPhone ? account.ownerPhone : '-';

                    return `
                    <tr>
                        <td>${account.phone}</td>
                        <td>
                            <span class="status-badge ${isSubAccount ? 'status-sub-account' : 'status-main-account'}">
                            ${accountType}
                            </span>
                        </td>
                        <td>${mainAccountInfo}</td>
                        <td>${registerTimeStr}</td>
                        <td>${lastLoginTimeStr}</td>
                        <td>
                            <span class="status-badge ${account.isOnline ? 'status-online' : 'status-offline'}">
                            ${account.isOnline ? '在线' : '离线'}
                            </span>
                        </td>
                        <td>
                            <span class="status-badge ${hasExpiry ? 'status-activated' : 'status-inactive'}">
                            ${activationStatus}
                            </span>
                        </td>
                        <td>${expiryDateStr}</td>
                        <td>
                            <button class="button button-danger button-small" onclick="deleteAccount('${account.phone}')">删除</button>
                            ${account.isOnline ? `<button class="button button-warning button-small" onclick="forceLogout('${account.phone}')">强制下线</button>` : ''}
                        </td>
                    </tr>
                    `;
                }).join('');
            } else {
                showAdminMessage(data.message || '加载账号列表失败', 'error');
                accountsTableBody.innerHTML = `<tr><td colspan="9" class="text-center">加载失败: ${data.message || '未知错误'}</td></tr>`;
            }
        } catch (error) {
            if (error.message !== 'Unauthorized') {
              showAdminMessage('网络错误或请求失败，请稍后重试。', 'error');
            }
            accountsTableBody.innerHTML = `<tr><td colspan="9" class="text-center">加载账号列表时发生错误。</td></tr>`;
        }
    }

    async function loadActivationCodes(type = '', used = '') {
        try {
            let url = `${API_URL}/api/admin/activation-codes`;
            const params = [];
            if (type) params.push(`type=${type}`);
            if (used !== '') params.push(`used=${used}`);
            if (params.length > 0) url += `?${params.join('&')}`;

            const data = await fetchWithAuth(url);

            if (data.success) {
                if (data.codes.length === 0) {
                    codesTableBody.innerHTML = `<tr><td colspan="7" class="text-center">没有符合条件的激活码。</td></tr>`;
                    return;
                }
                codesTableBody.innerHTML = data.codes.map(code => `
                    <tr>
                    <td>${code.code}</td>
                    <td>${code.type}天</td>
                    <td>${new Date(code.createdAt).toLocaleString()}</td>
                    <td>
                        <span class="status-badge ${code.usedAt ? 'status-used' : 'status-unused'}">
                        ${code.usedAt ? '已使用' : '未使用'}
                        </span>
                    </td>
                    <td>${code.usedAt ? new Date(code.usedAt).toLocaleString() : 'N/A'}</td>
                    <td>${code.usedBy || 'N/A'}</td>
                    <td>
                        ${!code.usedAt ? `<button class="button button-secondary button-small" onclick="copyCode('${code.code}')">复制</button>` : ''}
                    </td>
                    </tr>
                `).join('');
            } else {
                showAdminMessage(data.message || '加载激活码列表失败', 'error');
                codesTableBody.innerHTML = `<tr><td colspan="7" class="text-center">加载失败: ${data.message || '未知错误'}</td></tr>`;
            }
        } catch (error) {
            if (error.message !== 'Unauthorized') {
              showAdminMessage('网络错误，请稍后重试', 'error');
            }
            codesTableBody.innerHTML = `<tr><td colspan="7" class="text-center">加载激活码列表时发生错误。</td></tr>`;
        }
    }

    async function loadActivationRecords() {
        try {
            const data = await fetchWithAuth(`${API_URL}/api/admin/activation-records`);
            if (data.success) {
                 if (data.records.length === 0) {
                    recordsTableBody.innerHTML = `<tr><td colspan="4" class="text-center">暂无激活记录。</td></tr>`;
                    return;
                }
                recordsTableBody.innerHTML = data.records.map(record => `
                    <tr>
                    <td>${record.code}</td>
                    <td>${new Date(record.usedAt).toLocaleString()}</td>
                    <td>${record.userPhone || record.phone || 'N/A'}</td>
                    <td>${record.days}天</td>
                    </tr>
                `).join('');
            } else {
                showAdminMessage(data.message || '加载激活记录失败', 'error');
                recordsTableBody.innerHTML = `<tr><td colspan="4" class="text-center">加载失败: ${data.message || '未知错误'}</td></tr>`;
            }
        } catch (error) {
            if (error.message !== 'Unauthorized') {
                showAdminMessage('网络错误，请稍后重试', 'error');
            }
            recordsTableBody.innerHTML = `<tr><td colspan="4" class="text-center">加载激活记录时发生错误。</td></tr>`;
        }
    }

    // 平台账号信息管理函数
    async function loadAccountInfoList(platformPhone = '') {
        try {
            // 使用新的API获取所有主账号的平台账户信息
            let url = `${API_URL}/api/admin/platform-accounts?get_all_main_accounts=true`;
            if (platformPhone) {
                url += `&platform_phone=${encodeURIComponent(platformPhone)}`;
            }

            const data = await fetchWithAuth(url);
            if (data.success) {
                if (data.accountInfos.length === 0) {
                    accountInfoTableBody.innerHTML = `<tr><td colspan="17" class="text-center">暂无平台账号信息。</td></tr>`;
                    return;
                }
                accountInfoTableBody.innerHTML = data.accountInfos.map(info => {
                    // 正确处理stats字段，可能是字符串或对象
                    const stats = info.stats && typeof info.stats === 'object' ? info.stats :
                                 (typeof info.stats === 'string' ? JSON.parse(info.stats) : {});
                    return `
                        <tr>
                            <td>${info.holder_phone || '-'}</td>
                            <td>${info.phone}</td>
                            <td>${info.platform || '头条号'}</td>
                            <td>${info.username || '-'}</td>
                            <td>${info.login_type || '-'}</td>
                            <td>${info.team_tag || '-'}</td>
                            <td>${info.is_verified || '-'}</td>
                            <td>
                                <span class="account-status ${
                                    info.account_status === '正常' ? 'status-normal' :
                                    info.account_status === '掉线' ? 'status-offline' :
                                    'status-abnormal'
                                }">
                                    ${info.account_status || '-'}
                                </span>
                            </td>
                            <td class="number-cell">${stats.followers || '-'}</td>
                            <td class="number-cell">${stats.credit_score || '-'}</td>
                            <td class="number-cell">${stats.total_income || '-'}</td>
                            <td class="number-cell">${stats.yesterday_income || '-'}</td>
                            <td class="withdraw-amount-cell">${stats.can_withdraw_amount || '-'}</td>
                            <td class="number-cell">${stats.total_reads || '-'}</td>
                            <td class="number-cell">${stats.yesterday_reads || '-'}</td>
                            <td class="number-cell">${info.drafts_count || '-'}</td>
                            <td>
                                <button class="button button-primary" onclick="editAccountInfo('${info.phone}', ${info.main_account_id})">编辑</button>
                                <button class="button button-danger" onclick="deleteAccountInfo('${info.phone}', ${info.main_account_id})">删除</button>
                                <button class="button button-warning" onclick="showTransferModal('${info.phone}', ${info.main_account_id})">转移</button>
                            </td>
                        </tr>
                    `;
                }).join('');
            } else {
                showAdminMessage(data.message || '加载平台账号信息失败', 'error');
                accountInfoTableBody.innerHTML = `<tr><td colspan="17" class="text-center">加载失败: ${data.message || '未知错误'}</td></tr>`;
            }
        } catch (error) {
            if (error.message !== 'Unauthorized') {
                showAdminMessage('网络错误，请稍后重试', 'error');
            }
            accountInfoTableBody.innerHTML = `<tr><td colspan="17" class="text-center">加载平台账号信息时发生错误。</td></tr>`;
        }
    }

    function showAccountInfoModal(isEdit = false, accountInfo = null) {
        // 确保模态框元素存在
        const modal = document.getElementById('account-info-modal');
        const title = document.getElementById('account-info-modal-title');
        const message = document.getElementById('account-info-message');

        if (!modal) {
            console.error('找不到模态框元素');
            return;
        }

        if (title) {
            title.textContent = isEdit ? '编辑平台账号信息' : '添加平台账号信息';
        }

        if (message) {
            message.classList.add('hidden');
        }

        if (isEdit && accountInfo) {
            // 编辑模式：将账号信息转换为JSON格式显示
            currentEditingAccountId = accountInfo.phone;

            // 构建完整的JSON数据，确保所有字段都有默认值
            const jsonData = {
                phone: accountInfo.phone || '',
                platform: accountInfo.platform || '头条号',
                login_type: accountInfo.login_type || '',
                team_tag: accountInfo.team_tag || '',
                data_update_time: accountInfo.data_update_time || '',
                login_time: accountInfo.login_time || '',
                username: accountInfo.username || '',
                sessionid: accountInfo.sessionid || '',
                homepage_url: accountInfo.homepage_url || '',
                is_verified: accountInfo.is_verified || '否',
                drafts_count: accountInfo.drafts_count || '',
                owner_id: accountInfo.owner_id || accountInfo.main_account_id || '',
                current_holder_id: accountInfo.current_holder_id || accountInfo.main_account_id || '',
                created_at: accountInfo.created_at || '',
                updated_at: accountInfo.updated_at || '',
                stats: {
                    followers: (accountInfo.stats && accountInfo.stats.followers) || '0',
                    total_reads: (accountInfo.stats && accountInfo.stats.total_reads) || '0',
                    total_income: (accountInfo.stats && accountInfo.stats.total_income) || '0',
                    yesterday_reads: (accountInfo.stats && accountInfo.stats.yesterday_reads) || '0',
                    yesterday_income: (accountInfo.stats && accountInfo.stats.yesterday_income) || '0',
                    credit_score: (accountInfo.stats && accountInfo.stats.credit_score) || '0',
                    can_withdraw_amount: (accountInfo.stats && accountInfo.stats.can_withdraw_amount) || '0'
                },
                account_status: accountInfo.account_status || '正常'
            };

            accountJsonDataInput.value = JSON.stringify(jsonData, null, 2);
        } else {
            currentEditingAccountId = null;
            clearAccountInfoForm();
        }

        modal.classList.remove('hidden');
    }

    function hideAccountInfoModal() {
        const modal = document.getElementById('account-info-modal');
        if (modal) {
            modal.classList.add('hidden');
        }
        clearAccountInfoForm();
        currentEditingAccountId = null;
        window.currentEditingMainAccountId = null; // 清理主账号ID
    }

    function clearAccountInfoForm() {
        accountJsonDataInput.value = '';
    }

    // 定时任务测试相关函数
    function showScheduledTaskModal() {
        scheduledTaskModal.classList.remove('hidden');
        taskStatus.textContent = '未开始';
        taskStatus.className = 'task-status';
        clearTaskLogs();
    }

    function hideScheduledTaskModal() {
        scheduledTaskModal.classList.add('hidden');
        stopScheduledTask();
    }

    function addTaskLog(message, type = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = document.createElement('div');
        logEntry.className = 'log-entry';

        const timestampSpan = document.createElement('span');
        timestampSpan.className = 'log-timestamp';
        timestampSpan.textContent = `[${timestamp}] `;

        const messageSpan = document.createElement('span');
        messageSpan.className = `log-${type}`;
        messageSpan.textContent = message;

        logEntry.appendChild(timestampSpan);
        logEntry.appendChild(messageSpan);
        taskLogs.appendChild(logEntry);

        // 自动滚动到底部
        taskLogs.scrollTop = taskLogs.scrollHeight;
    }

    function clearTaskLogs() {
        taskLogs.innerHTML = '';
    }

    function updateTaskStatus(status, className) {
        taskStatus.textContent = status;
        taskStatus.className = `task-status ${className}`;
    }

    async function startScheduledTask() {
        const delay = parseInt(taskDelayInput.value) || 5;

        if (delay < 1 || delay > 300) {
            showAdminMessage('延迟时间必须在1-300秒之间', 'error');
            return;
        }

        // 更新UI状态
        startScheduledTestBtn.disabled = true;
        stopScheduledTestBtn.disabled = false;
        taskDelayInput.disabled = true;

        updateTaskStatus(`等待中 (${delay}秒后执行)`, 'waiting');
        addTaskLog(`定时任务测试开始，将在 ${delay} 秒后执行`, 'info');

        let countdown = delay;
        const countdownInterval = setInterval(() => {
            countdown--;
            updateTaskStatus(`等待中 (${countdown}秒后执行)`, 'waiting');
            addTaskLog(`倒计时: ${countdown} 秒`, 'info');

            if (countdown <= 0) {
                clearInterval(countdownInterval);
            }
        }, 1000);

        // 设置定时任务
        scheduledTaskTimer = setTimeout(async () => {
            updateTaskStatus('执行中...', 'running');
            addTaskLog('开始执行定时任务...', 'info');

            try {
                // 执行实际的定时任务逻辑
                await executeScheduledTask();

                updateTaskStatus('完成', 'completed');
                addTaskLog('定时任务执行完成', 'success');

            } catch (error) {
                updateTaskStatus('错误', 'error');
                addTaskLog(`定时任务执行失败: ${error.message}`, 'error');
            } finally {
                // 重置UI状态
                startScheduledTestBtn.disabled = false;
                stopScheduledTestBtn.disabled = true;
                taskDelayInput.disabled = false;
                scheduledTaskTimer = null;
            }
        }, delay * 1000);
    }

    function stopScheduledTask() {
        if (scheduledTaskTimer) {
            clearTimeout(scheduledTaskTimer);
            scheduledTaskTimer = null;

            updateTaskStatus('已停止', 'error');
            addTaskLog('定时任务已被手动停止', 'warning');

            // 重置UI状态
            startScheduledTestBtn.disabled = false;
            stopScheduledTestBtn.disabled = true;
            taskDelayInput.disabled = false;
        }
    }

    async function executeScheduledTask() {
        addTaskLog('正在初始化数据库...', 'info');
        await new Promise(resolve => setTimeout(resolve, 500)); // 模拟初始化延迟

        addTaskLog('开始批量获取平台账号数据...', 'info');

        try {
            // 调用实际的数据获取API
            const response = await fetchWithAuth(`${API_URL}/api/admin/manual-fetch-income`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });

            if (response.success) {
                const { totalAccounts, successCount, failureCount, skippedCount, results } = response;

                addTaskLog(`数据获取完成: 总计${totalAccounts}个账号`, 'success');
                addTaskLog(`成功: ${successCount}个, 失败: ${failureCount}个, 跳过: ${skippedCount}个`, 'info');

                // 显示详细结果
                if (results && results.length > 0) {
                    addTaskLog('详细结果:', 'info');
                    results.forEach((result, index) => {
                        const status = result.success ? '成功' : '失败';
                        const logType = result.success ? 'success' : 'error';
                        addTaskLog(`  ${index + 1}. ${result.phone}: ${status} - ${result.message}`, logType);
                    });
                }

                addTaskLog('定时任务执行成功完成', 'success');
            } else {
                throw new Error(response.message || '未知错误');
            }

        } catch (error) {
            addTaskLog(`API调用失败: ${error.message}`, 'error');
            throw error;
        }
    }

    // API压力测试相关函数
    function showApiPressureModal() {
        apiPressureModal.classList.remove('hidden');
        resetPressureTestStats();
        clearPressureTestLogs();
    }

    function hideApiPressureModal() {
        apiPressureModal.classList.add('hidden');
        stopPressureTest();
    }

    function resetPressureTestStats() {
        totalRequestsSpan.textContent = '0';
        successRequestsSpan.textContent = '0';
        failedRequestsSpan.textContent = '0';
        successRateSpan.textContent = '0%';
        avgResponseTimeSpan.textContent = '0ms';
        pressureTestStatusSpan.textContent = '未开始';
        pressureTestStatusSpan.className = 'test-status';
    }

    function updatePressureTestStats(stats) {
        totalRequestsSpan.textContent = stats.total;
        successRequestsSpan.textContent = stats.success;
        failedRequestsSpan.textContent = stats.failed;

        const successRate = stats.total > 0 ? ((stats.success / stats.total) * 100).toFixed(1) : 0;
        successRateSpan.textContent = `${successRate}%`;

        const avgTime = stats.responseTimes.length > 0 ?
            (stats.responseTimes.reduce((a, b) => a + b, 0) / stats.responseTimes.length).toFixed(0) : 0;
        avgResponseTimeSpan.textContent = `${avgTime}ms`;
    }

    function addPressureTestLog(message, type = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = document.createElement('div');
        logEntry.className = 'log-entry';

        const timestampSpan = document.createElement('span');
        timestampSpan.className = 'log-timestamp';
        timestampSpan.textContent = `[${timestamp}] `;

        const messageSpan = document.createElement('span');
        messageSpan.className = `log-${type}`;
        messageSpan.textContent = message;

        logEntry.appendChild(timestampSpan);
        logEntry.appendChild(messageSpan);
        pressureTestLogs.appendChild(logEntry);

        // 自动滚动到底部
        pressureTestLogs.scrollTop = pressureTestLogs.scrollHeight;
    }

    function clearPressureTestLogs() {
        pressureTestLogs.innerHTML = '';
    }

    async function startPressureTest() {
        const sessionid = testSessionidInput.value.trim();
        const testCount = parseInt(testCountInput.value) || 10;
        const interval = parseInt(testIntervalInput.value) || 1000;

        if (!sessionid) {
            showAdminMessage('请输入有效的SessionID', 'error');
            return;
        }

        if (testCount < 1 || testCount > 1000) {
            showAdminMessage('测试数量必须在1-1000之间', 'error');
            return;
        }

        if (interval < 0 || interval > 10000) {
            showAdminMessage('请求间隔必须在0-10000毫秒之间', 'error');
            return;
        }

        // 更新UI状态
        startPressureTestBtn.disabled = true;
        stopPressureTestBtn.disabled = false;
        testSessionidInput.disabled = true;
        testCountInput.disabled = true;
        testIntervalInput.disabled = true;
        pressureTestRunning = true;

        pressureTestStatusSpan.textContent = '运行中';
        pressureTestStatusSpan.className = 'test-status running';

        addPressureTestLog(`开始API压力测试: ${testCount}次请求，间隔${interval}ms`, 'summary');
        addPressureTestLog(`使用SessionID: ${sessionid.substring(0, 8)}...`, 'request');

        const stats = {
            total: 0,
            success: 0,
            failed: 0,
            responseTimes: []
        };

        try {
            for (let i = 1; i <= testCount && pressureTestRunning; i++) {
                const requestStart = Date.now();

                addPressureTestLog(`请求 ${i}/${testCount}: 开始调用收益API...`, 'request');

                try {
                    const response = await fetchIncomeData(sessionid);
                    const requestEnd = Date.now();
                    // 使用后端返回的响应时间，如果没有则使用前端计算的时间
                    const responseTime = response.responseTime || (requestEnd - requestStart);

                    stats.total++;
                    stats.responseTimes.push(responseTime);

                    if (response.code === 0) {
                        stats.success++;
                        addPressureTestLog(`请求 ${i}: 成功 (${responseTime}ms) - ${response.message}`, 'response-success');
                        if (response.data && response.data.length > 0) {
                            addPressureTestLog(`  数据: ${JSON.stringify(response.data[0])}`, 'response-success');
                        }
                    } else {
                        stats.failed++;
                        addPressureTestLog(`请求 ${i}: 失败 (${responseTime}ms) - Code: ${response.code}, Message: ${response.message}`, 'response-error');
                    }

                } catch (error) {
                    const requestEnd = Date.now();
                    const responseTime = requestEnd - requestStart;

                    stats.total++;
                    stats.failed++;
                    stats.responseTimes.push(responseTime);

                    addPressureTestLog(`请求 ${i}: 异常 (${responseTime}ms) - ${error.message}`, 'response-error');
                }

                // 更新统计信息
                updatePressureTestStats(stats);

                // 等待间隔时间（除了最后一次请求）
                if (i < testCount && pressureTestRunning && interval > 0) {
                    addPressureTestLog(`等待 ${interval}ms...`, 'timing');
                    await new Promise(resolve => setTimeout(resolve, interval));
                }
            }

            if (pressureTestRunning) {
                pressureTestStatusSpan.textContent = '完成';
                pressureTestStatusSpan.className = 'test-status completed';
                addPressureTestLog(`测试完成! 总计: ${stats.total}, 成功: ${stats.success}, 失败: ${stats.failed}`, 'summary');
            } else {
                pressureTestStatusSpan.textContent = '已停止';
                pressureTestStatusSpan.className = 'test-status stopped';
                addPressureTestLog('测试被手动停止', 'summary');
            }

        } catch (error) {
            pressureTestStatusSpan.textContent = '错误';
            pressureTestStatusSpan.className = 'test-status stopped';
            addPressureTestLog(`测试发生错误: ${error.message}`, 'response-error');
        } finally {
            // 重置UI状态
            startPressureTestBtn.disabled = false;
            stopPressureTestBtn.disabled = true;
            testSessionidInput.disabled = false;
            testCountInput.disabled = false;
            testIntervalInput.disabled = false;
            pressureTestRunning = false;
        }
    }

    function stopPressureTest() {
        if (pressureTestRunning) {
            pressureTestRunning = false;
            addPressureTestLog('正在停止测试...', 'summary');
        }
    }

    // 收益数据获取函数（用于压力测试）- 通过后端代理调用
    async function fetchIncomeData(sessionid) {
        const response = await fetchWithAuth(`${API_URL}/api/admin/test-income-data`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                sessionid: sessionid
            })
        });

        if (!response.success) {
            throw new Error(response.message || '未知错误');
        }

        // 返回与原API相同的格式
        return {
            code: response.code,
            message: response.message,
            data: response.data,
            responseTime: response.responseTime
        };
    }

    // ==================== Durable Objects + Alarms 批量处理 ====================

    let currentBatchId = null;
    let batchEventSource = null;
    let batchStatusPollingInterval = null;

    // 启动 Durable Objects 批量处理
    async function startDurableBatchUpdate() {
        const btn = document.getElementById('durable-batch-update-btn');
        const statusBtn = document.getElementById('batch-status-btn');
        const cancelBtn = document.getElementById('cancel-batch-btn');
        const originalText = btn.textContent;

        try {
            // 更新按钮状态
            btn.textContent = '🚀 启动中...';
            btn.disabled = true;

            console.log('=== 开始 Durable Objects 批量处理 ===');
            showAdminMessage('启动 Durable Objects + Alarms 批量处理，请稍候...', 'info');

            // 配置批量处理参数
            const config = {
                batchSize: 4,              // 每批4个账号
                delayBetweenBatches: 500,  // 批次间延迟0.5秒
                delayBetweenAccounts: 100  // 账号间延迟0.1秒
            };

            // 调用新的 Durable Objects API
            const response = await fetchWithAuth(`${API_URL}/api/admin/start-durable-batch-update`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ config })
            });

            console.log('API响应:', response);

            if (response.success) {
                currentBatchId = response.batchId;
                console.log('批量处理启动成功:', currentBatchId);

                showAdminMessage(`批量处理启动成功！批次ID: ${currentBatchId}，总账号数: ${response.totalAccounts}，分${response.totalBatches}批处理`, 'success');

                // 显示进度容器
                const progressContainer = document.getElementById('batch-progress-container');
                progressContainer.classList.remove('hidden');

                // 更新按钮状态
                btn.textContent = '🚀 处理中...';
                btn.disabled = true;
                statusBtn.disabled = false;
                cancelBtn.disabled = false;

                // 开始 SSE 连接监听进度
                startBatchSSEConnection(currentBatchId);

                // 开始状态轮询作为备用
                startBatchStatusPolling();

            } else {
                console.error('启动失败:', response.message);
                showAdminMessage(`启动失败: ${response.message}`, 'error');

                // 恢复按钮状态
                btn.textContent = originalText;
                btn.disabled = false;
            }
        } catch (error) {
            console.error('启动异常:', error);
            showAdminMessage(`启动失败: ${error.message}`, 'error');

            // 恢复按钮状态
            btn.textContent = originalText;
            btn.disabled = false;
        }
    }

    // 启动 SSE 连接监听批量处理进度
    function startBatchSSEConnection(batchId) {
        if (batchEventSource) {
            batchEventSource.close();
        }

        console.log(`建立批量处理 SSE 连接: ${batchId}`);

        try {
            batchEventSource = new EventSource(`${API_URL}/api/admin/batch-progress-sse?batchId=${batchId}&token=${localStorage.getItem('admin_token')}`);

            batchEventSource.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);

                    if (data.type === 'connected') {
                        console.log('批量处理 SSE 连接已建立');
                        showAdminMessage('实时进度连接已建立', 'info');
                    } else if (data.type === 'progress') {
                        if (data.data) {
                            updateBatchProgress(data.data);
                            console.log(`进度更新: ${data.data.processedAccounts}/${data.data.totalAccounts}`);
                        }
                    }
                } catch (error) {
                    console.error('SSE 数据解析错误:', error);
                }
            };

            batchEventSource.onerror = function(event) {
                console.error('批量处理 SSE 连接错误:', event);
                showAdminMessage('实时进度连接中断，将使用轮询方式', 'warning');
                batchEventSource.close();
                batchEventSource = null;
            };
        } catch (error) {
            console.error('SSE 连接创建失败:', error);
            showAdminMessage('无法建立实时连接，使用轮询方式', 'warning');
        }
    }

    // 开始状态轮询
    function startBatchStatusPolling() {
        if (batchStatusPollingInterval) {
            clearInterval(batchStatusPollingInterval);
        }

        batchStatusPollingInterval = setInterval(async () => {
            try {
                const response = await fetchWithAuth(`${API_URL}/api/admin/batch-status`);

                if (response.success && response.data) {
                    updateBatchProgress(response.data);

                    // 如果处理完成，停止轮询
                    if (response.data.status === 'completed' || response.data.status === 'failed' || response.data.status === 'cancelled') {
                        clearInterval(batchStatusPollingInterval);
                        batchStatusPollingInterval = null;

                        if (batchEventSource) {
                            batchEventSource.close();
                            batchEventSource = null;
                        }

                        onBatchCompleted(response.data);
                    }
                }
            } catch (error) {
                console.error('状态轮询错误:', error);
            }
        }, 2000); // 每2秒轮询一次
    }

    // 更新批量处理进度显示
    function updateBatchProgress(progress) {
        if (!progress) {
            return;
        }

        const percentage = progress.totalAccounts > 0
            ? Math.round((progress.processedAccounts / progress.totalAccounts) * 100)
            : 0;

        // 更新进度条
        const progressFill = document.getElementById('batch-progress-fill');
        const progressText = document.getElementById('batch-progress-text');

        if (progressFill) {
            progressFill.style.width = `${percentage}%`;
        }

        if (progressText) {
            let text = `${progress.processedAccounts}/${progress.totalAccounts} (${percentage}%)`;
            if (progress.currentAccount) {
                text += ` - 正在处理: ${progress.currentAccount}`;
            }
            progressText.textContent = text;
        }

        // 更新统计信息
        // 修复批次显示：完成状态下显示总批次数，处理中显示当前批次
        const currentBatchDisplay = progress.status === 'completed'
            ? `已完成: ${progress.totalBatches}/${progress.totalBatches}`
            : `当前批次: ${progress.currentBatch + 1}/${progress.totalBatches}`;

        const elements = {
            'batch-total-accounts': `总账号: ${progress.totalAccounts}`,
            'batch-processed-accounts': `已处理: ${progress.processedAccounts}`,
            'batch-success-count': `成功: ${progress.successCount}`,
            'batch-failure-count': `失败: ${progress.failureCount}`,
            'batch-current-batch': currentBatchDisplay,
            'batch-status': `状态: ${progress.status}`
        };

        Object.entries(elements).forEach(([id, text]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = text;
            }
        });

        // 更新当前处理账号
        const currentAccountEl = document.getElementById('batch-current-account');
        if (currentAccountEl) {
            currentAccountEl.textContent = progress.currentAccount
                ? `当前处理: ${progress.currentAccount}`
                : '当前处理: -';
        }
    }

    // 批量处理完成回调
    function onBatchCompleted(progress) {
        const btn = document.getElementById('durable-batch-update-btn');
        const statusBtn = document.getElementById('batch-status-btn');
        const cancelBtn = document.getElementById('cancel-batch-btn');

        // 恢复按钮状态
        btn.textContent = '🚀 一键更新全部信息 (DO+Alarms)';
        btn.disabled = false;
        statusBtn.disabled = true;
        cancelBtn.disabled = true;

        // 显示完成消息
        if (progress.status === 'completed') {
            showAdminMessage(`批量处理完成！总计${progress.totalAccounts}个账号，成功${progress.successCount}个，失败${progress.failureCount}个`, 'success');

            // 刷新账号列表
            setTimeout(() => {
                loadAccountInfoList();
            }, 2000);
        } else if (progress.status === 'failed') {
            showAdminMessage(`批量处理失败：${progress.errorMessage || '未知错误'}`, 'error');
        } else if (progress.status === 'cancelled') {
            showAdminMessage('批量处理已取消', 'warning');
        }

        currentBatchId = null;
    }

    // 获取批量处理状态
    async function getBatchStatus() {
        try {
            const response = await fetchWithAuth(`${API_URL}/api/admin/batch-status`);

            if (response.success) {
                if (response.data) {
                    updateBatchProgress(response.data);
                    showAdminMessage('状态获取成功', 'info');

                    // 显示进度容器
                    const progressContainer = document.getElementById('batch-progress-container');
                    progressContainer.classList.remove('hidden');
                } else {
                    showAdminMessage('当前没有正在进行的批量处理', 'info');
                }
            } else {
                showAdminMessage('状态获取失败', 'error');
            }
        } catch (error) {
            console.error('状态获取异常:', error);
            showAdminMessage(`状态获取失败: ${error.message}`, 'error');
        }
    }

    // 取消批量处理
    async function cancelBatchUpdate() {
        const cancelBtn = document.getElementById('cancel-batch-btn');
        const originalText = cancelBtn.textContent;

        try {
            cancelBtn.textContent = '❌ 取消中...';
            cancelBtn.disabled = true;

            const response = await fetchWithAuth(`${API_URL}/api/admin/cancel-batch-update`, {
                method: 'POST'
            });

            if (response.success) {
                showAdminMessage('批量处理已取消', 'warning');

                // 停止轮询和 SSE
                if (batchStatusPollingInterval) {
                    clearInterval(batchStatusPollingInterval);
                    batchStatusPollingInterval = null;
                }

                if (batchEventSource) {
                    batchEventSource.close();
                    batchEventSource = null;
                }

                // 恢复按钮状态
                const btn = document.getElementById('durable-batch-update-btn');
                const statusBtn = document.getElementById('batch-status-btn');

                btn.textContent = '🚀 一键更新全部信息 (DO+Alarms)';
                btn.disabled = false;
                statusBtn.disabled = true;
                cancelBtn.disabled = true;

                currentBatchId = null;
            } else {
                showAdminMessage(`取消失败: ${response.message}`, 'error');
                cancelBtn.textContent = originalText;
                cancelBtn.disabled = false;
            }
        } catch (error) {
            console.error('取消异常:', error);
            showAdminMessage(`取消失败: ${error.message}`, 'error');
            cancelBtn.textContent = originalText;
            cancelBtn.disabled = false;
        }
    }

    // 重置批量处理状态
    async function resetBatchState() {
        const resetBtn = document.getElementById('reset-batch-btn');
        const originalText = resetBtn.textContent;

        try {
            resetBtn.textContent = '🔄 重置中...';
            resetBtn.disabled = true;

            const response = await fetchWithAuth(`${API_URL}/api/admin/reset-batch-state`, {
                method: 'POST'
            });

            if (response.success) {
                showAdminMessage('批量处理状态已重置，现在可以重新启动', 'success');

                // 重置前端状态
                currentBatchId = null;

                // 停止轮询和 SSE
                if (batchStatusPollingInterval) {
                    clearInterval(batchStatusPollingInterval);
                    batchStatusPollingInterval = null;
                }

                if (batchEventSource) {
                    batchEventSource.close();
                    batchEventSource = null;
                }

                // 恢复按钮状态
                const btn = document.getElementById('durable-batch-update-btn');
                const statusBtn = document.getElementById('batch-status-btn');
                const cancelBtn = document.getElementById('cancel-batch-btn');

                btn.textContent = '🚀 一键更新全部信息 (DO+Alarms)';
                btn.disabled = false;
                statusBtn.disabled = true;
                cancelBtn.disabled = true;

                // 隐藏进度容器
                const progressContainer = document.getElementById('batch-progress-container');
                progressContainer.classList.add('hidden');

            } else {
                showAdminMessage(`重置失败: ${response.message}`, 'error');
            }
        } catch (error) {
            console.error('重置异常:', error);
            showAdminMessage(`重置失败: ${error.message}`, 'error');
        } finally {
            resetBtn.textContent = originalText;
            resetBtn.disabled = false;
        }
    }

    // 手动更新全部信息函数
    async function manualUpdateAllData() {
        const btn = document.getElementById('manual-update-btn');
        const originalText = btn.textContent;

        try {
            // 更新按钮状态
            btn.textContent = '更新中...';
            btn.disabled = true;

            console.log('=== 开始手动更新全部信息 ===');
            showAdminMessage('开始更新全部信息（收益、信用分、粉丝数、禁言状态、实名状态），请稍候...', 'info');

            // 调用全部信息更新API
            const response = await fetchWithAuth(`${API_URL}/api/admin/manual-update-all-data`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });

            console.log('API响应:', response);

            if (response.success) {
                const { totalAccounts, successCount, failureCount, skippedCount, results } = response;

                console.log(`全部信息更新完成: 总计${totalAccounts}个账号，成功${successCount}个，失败${failureCount}个，跳过${skippedCount}个`);

                // 显示详细结果
                let detailMessage = `全部信息更新完成！\n总计: ${totalAccounts}个账号\n成功: ${successCount}个\n失败: ${failureCount}个\n跳过: ${skippedCount}个`;

                if (results && results.length > 0) {
                    console.log('详细结果:');
                    results.forEach((result, index) => {
                        console.log(`${index + 1}. ${result.phone}: ${result.success ? '成功' : '失败'} - ${result.message}`);
                        if (result.data) {
                            console.log(`   数据: 总收益=${result.data.stats?.total_income || '-'}, 昨日收益=${result.data.stats?.yesterday_income || '-'}, 可提现=${result.data.stats?.can_withdraw_amount || '-'}, 信用分=${result.data.stats?.credit_score || '-'}, 粉丝数=${result.data.stats?.followers || '-'}, 实名状态=${result.data.is_verified || '-'}, 账号状态=${result.data.account_status || '-'}`);
                        }
                    });

                    // 显示失败的账号
                    const failedAccounts = results.filter(r => !r.success);
                    if (failedAccounts.length > 0) {
                        detailMessage += '\n\n失败的账号:';
                        failedAccounts.forEach(account => {
                            detailMessage += `\n${account.phone}: ${account.message}`;
                        });
                    }
                }

                showAdminMessage(detailMessage, 'success');

                // 刷新账号列表
                setTimeout(() => {
                    loadAccountInfoList();
                }, 2000);

            } else {
                console.error('更新失败:', response.message);
                showAdminMessage(`更新失败: ${response.message}`, 'error');
            }

        } catch (error) {
            console.error('更新全部信息时发生错误:', error);
            showAdminMessage(`更新失败: ${error.message}`, 'error');
        } finally {
            // 恢复按钮状态
            btn.textContent = originalText;
            btn.disabled = false;
            console.log('=== 手动更新全部信息结束 ===');
        }
    }

    // 一键更新收益数据函数
    async function manualUpdateIncomeData() {
        const btn = document.getElementById('manual-update-income-btn');
        const originalText = btn.textContent;

        try {
            // 更新按钮状态
            btn.textContent = '更新中...';
            btn.disabled = true;

            console.log('=== 开始手动更新收益数据 ===');
            showAdminMessage('开始更新收益数据，请稍候...', 'info');

            // 调用收益更新API
            const response = await fetchWithAuth(`${API_URL}/api/admin/manual-update-income`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });

            console.log('API响应:', response);

            if (response.success) {
                const { totalAccounts, successCount, failureCount, skippedCount, results } = response;

                console.log(`收益数据更新完成: 总计${totalAccounts}个账号，成功${successCount}个，失败${failureCount}个，跳过${skippedCount}个`);

                // 显示详细结果
                let detailMessage = `收益数据更新完成！\n总计: ${totalAccounts}个账号\n成功: ${successCount}个\n失败: ${failureCount}个\n跳过: ${skippedCount}个`;

                if (results && results.length > 0) {
                    // 显示失败的账号
                    const failedAccounts = results.filter(r => !r.success);
                    if (failedAccounts.length > 0) {
                        detailMessage += '\n\n失败的账号:';
                        failedAccounts.forEach(account => {
                            detailMessage += `\n${account.phone}: ${account.message}`;
                        });
                    }
                }

                showAdminMessage(detailMessage, 'success');

                // 刷新账号列表
                setTimeout(() => {
                    loadAccountInfoList();
                }, 2000);

            } else {
                console.error('更新失败:', response.message);
                showAdminMessage(`更新失败: ${response.message}`, 'error');
            }

        } catch (error) {
            console.error('更新收益数据时发生错误:', error);
            showAdminMessage(`更新失败: ${error.message}`, 'error');
        } finally {
            // 恢复按钮状态
            btn.textContent = originalText;
            btn.disabled = false;
            console.log('=== 手动更新收益数据结束 ===');
        }
    }

    // 一键更新信用分和粉丝数函数
    async function manualUpdateCreditAndFansData() {
        const btn = document.getElementById('manual-update-credit-fans-btn');
        const originalText = btn.textContent;

        try {
            // 更新按钮状态
            btn.textContent = '更新中...';
            btn.disabled = true;

            console.log('=== 开始手动更新信用分和粉丝数 ===');
            showAdminMessage('开始更新信用分和粉丝数，请稍候...', 'info');

            // 调用信用分和粉丝数更新API
            const response = await fetchWithAuth(`${API_URL}/api/admin/manual-update-credit-fans`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });

            console.log('API响应:', response);

            if (response.success) {
                const { totalAccounts, successCount, failureCount, skippedCount, results } = response;

                console.log(`信用分和粉丝数更新完成: 总计${totalAccounts}个账号，成功${successCount}个，失败${failureCount}个，跳过${skippedCount}个`);

                // 显示详细结果
                let detailMessage = `信用分和粉丝数更新完成！\n总计: ${totalAccounts}个账号\n成功: ${successCount}个\n失败: ${failureCount}个\n跳过: ${skippedCount}个`;

                if (results && results.length > 0) {
                    // 显示失败的账号
                    const failedAccounts = results.filter(r => !r.success);
                    if (failedAccounts.length > 0) {
                        detailMessage += '\n\n失败的账号:';
                        failedAccounts.forEach(account => {
                            detailMessage += `\n${account.phone}: ${account.message}`;
                        });
                    }
                }

                showAdminMessage(detailMessage, 'success');

                // 刷新账号列表
                setTimeout(() => {
                    loadAccountInfoList();
                }, 2000);

            } else {
                console.error('更新失败:', response.message);
                showAdminMessage(`更新失败: ${response.message}`, 'error');
            }

        } catch (error) {
            console.error('更新信用分和粉丝数时发生错误:', error);
            showAdminMessage(`更新失败: ${error.message}`, 'error');
        } finally {
            // 恢复按钮状态
            btn.textContent = originalText;
            btn.disabled = false;
            console.log('=== 手动更新信用分和粉丝数结束 ===');
        }
    }

    // 一键更新账号详细信息函数
    async function manualUpdateAccountInfoData() {
        const btn = document.getElementById('manual-update-account-info-btn');
        const originalText = btn.textContent;

        try {
            // 更新按钮状态
            btn.textContent = '更新中...';
            btn.disabled = true;

            console.log('=== 开始手动更新账号详细信息 ===');
            showAdminMessage('开始更新账号详细信息（用户名、主页URL、实名状态等），请稍候...', 'info');

            // 调用账号详细信息更新API
            const response = await fetchWithAuth(`${API_URL}/api/admin/manual-update-account-info`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });

            console.log('API响应:', response);

            if (response.success) {
                const { totalAccounts, successCount, failureCount, skippedCount, results } = response;

                console.log(`账号详细信息更新完成: 总计${totalAccounts}个账号，成功${successCount}个，失败${failureCount}个，跳过${skippedCount}个`);

                // 显示详细结果
                let detailMessage = `账号详细信息更新完成！\n总计: ${totalAccounts}个账号\n成功: ${successCount}个\n失败: ${failureCount}个\n跳过: ${skippedCount}个`;

                if (results && results.length > 0) {
                    // 显示失败的账号
                    const failedAccounts = results.filter(r => !r.success);
                    if (failedAccounts.length > 0) {
                        detailMessage += '\n\n失败的账号:';
                        failedAccounts.forEach(account => {
                            detailMessage += `\n${account.phone}: ${account.message}`;
                        });
                    }
                }

                showAdminMessage(detailMessage, 'success');

                // 刷新账号列表
                setTimeout(() => {
                    loadAccountInfoList();
                }, 2000);

            } else {
                console.error('更新失败:', response.message);
                showAdminMessage(`更新失败: ${response.message}`, 'error');
            }

        } catch (error) {
            console.error('更新账号详细信息时发生错误:', error);
            showAdminMessage(`更新失败: ${error.message}`, 'error');
        } finally {
            // 恢复按钮状态
            btn.textContent = originalText;
            btn.disabled = false;
            console.log('=== 手动更新账号详细信息结束 ===');
        }
    }

    // 一键更新账号状态函数
    async function manualUpdateAccountStatusData() {
        const btn = document.getElementById('manual-update-account-status-btn');
        const originalText = btn.textContent;

        try {
            // 更新按钮状态
            btn.textContent = '更新中...';
            btn.disabled = true;

            console.log('=== 开始手动更新账号状态 ===');
            showAdminMessage('开始更新账号状态（禁言状态等），请稍候...', 'info');

            // 调用账号状态更新API
            const response = await fetchWithAuth(`${API_URL}/api/admin/manual-update-account-status`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });

            console.log('API响应:', response);

            if (response.success) {
                const { totalAccounts, successCount, failureCount, skippedCount, results } = response;

                console.log(`账号状态更新完成: 总计${totalAccounts}个账号，成功${successCount}个，失败${failureCount}个，跳过${skippedCount}个`);

                // 显示详细结果
                let detailMessage = `账号状态更新完成！\n总计: ${totalAccounts}个账号\n成功: ${successCount}个\n失败: ${failureCount}个\n跳过: ${skippedCount}个`;

                if (results && results.length > 0) {
                    // 显示失败的账号
                    const failedAccounts = results.filter(r => !r.success);
                    if (failedAccounts.length > 0) {
                        detailMessage += '\n\n失败的账号:';
                        failedAccounts.forEach(account => {
                            detailMessage += `\n${account.phone}: ${account.message}`;
                        });
                    }
                }

                showAdminMessage(detailMessage, 'success');

                // 刷新账号列表
                setTimeout(() => {
                    loadAccountInfoList();
                }, 2000);

            } else {
                console.error('更新失败:', response.message);
                showAdminMessage(`更新失败: ${response.message}`, 'error');
            }

        } catch (error) {
            console.error('更新账号状态时发生错误:', error);
            showAdminMessage(`更新失败: ${error.message}`, 'error');
        } finally {
            // 恢复按钮状态
            btn.textContent = originalText;
            btn.disabled = false;
            console.log('=== 手动更新账号状态结束 ===');
        }
    }

    // 一键更新阅读量数据函数
    async function manualUpdateReadingData() {
        const btn = document.getElementById('manual-update-reading-data-btn');
        const originalText = btn.textContent;

        try {
            // 更新按钮状态
            btn.textContent = '更新中...';
            btn.disabled = true;

            console.log('=== 开始手动更新阅读量数据 ===');
            showAdminMessage('开始更新阅读量数据（总阅读量、昨日阅读量），请稍候...', 'info');

            // 调用阅读量数据更新API
            const response = await fetchWithAuth(`${API_URL}/api/admin/manual-update-reading-data`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });

            console.log('API响应:', response);

            if (response.success) {
                showAdminMessage(`阅读量数据更新完成！总计${response.totalAccounts}个账号，成功${response.successCount}个，失败${response.failureCount}个，跳过${response.skippedCount}个`, 'success');

                // 刷新账号列表
                await loadAccountInfoList();
            } else {
                showAdminMessage(`阅读量数据更新失败: ${response.message}`, 'error');
            }

        } catch (error) {
            console.error('阅读量数据更新异常:', error);
            showAdminMessage(`阅读量数据更新异常: ${error.message}`, 'error');
        } finally {
            // 恢复按钮状态
            btn.textContent = originalText;
            btn.disabled = false;
            console.log('=== 手动更新阅读量数据结束 ===');
        }
    }

    // 一键更新草稿箱数量函数
    async function manualUpdateDraftsCount() {
        const btn = document.getElementById('manual-update-drafts-count-btn');
        const originalText = btn.textContent;

        try {
            // 更新按钮状态
            btn.textContent = '更新中...';
            btn.disabled = true;

            console.log('=== 开始手动更新草稿箱数量 ===');
            showAdminMessage('开始更新草稿箱数量，请稍候...', 'info');

            // 调用草稿箱数量更新API
            const response = await fetchWithAuth(`${API_URL}/api/admin/manual-update-drafts-count`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });

            console.log('API响应:', response);

            if (response.success) {
                showAdminMessage(`草稿箱数量更新完成！总计${response.totalAccounts}个账号，成功${response.successCount}个，失败${response.failureCount}个，跳过${response.skippedCount}个`, 'success');

                // 刷新账号列表
                await loadAccountInfoList();
            } else {
                showAdminMessage(`草稿箱数量更新失败: ${response.message}`, 'error');
            }

        } catch (error) {
            console.error('草稿箱数量更新异常:', error);
            showAdminMessage(`草稿箱数量更新异常: ${error.message}`, 'error');
        } finally {
            // 恢复按钮状态
            btn.textContent = originalText;
            btn.disabled = false;
            console.log('=== 手动更新草稿箱数量结束 ===');
        }
    }

    async function generateActivationCodes() {
        try {
            const type = parseInt(newCodeType.value);
            const count = parseInt(newCodeCount.value);

            if (isNaN(type) || isNaN(count) || count < 1 || count > 100) {
                showAdminMessage('请输入有效的数量 (1-100)。', 'error');
                return;
            }

            const data = await fetchWithAuth(`${API_URL}/api/admin/generate-activation-codes`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ type, count })
            });

            if (data.success) {
                showAdminMessage(`成功生成 ${count} 个 ${type} 天的激活码。`, 'success');
                if (data.codes && data.codes.length > 0) {
                    generatedCodes.textContent = data.codes.join('\n');
                    generatedCodes.classList.remove('hidden');
                }
                loadActivationCodes(); // Refresh list
                newCodeCount.value = 1; // Reset count
            } else {
                showAdminMessage(data.message || '生成激活码失败。', 'error');
            }
        } catch (error) {
             if (error.message !== 'Unauthorized') {
                showAdminMessage('网络错误，请稍后重试。', 'error');
            }
        }
    }

    async function deleteAccount(phone) {
        if (!confirm(`确定要删除手机号为 ${phone} 的账号吗？此操作不可恢复。`)) {
            return;
        }
        try {
            const data = await fetchWithAuth(`${API_URL}/api/admin/delete-account`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ phone })
            });
            if (data.success) {
                showAdminMessage('账号删除成功。', 'success');
                loadAccounts();
            } else {
                showAdminMessage(data.message || '删除账号失败。', 'error');
            }
        } catch (error) {
            if (error.message !== 'Unauthorized') {
                showAdminMessage('网络错误，请稍后重试。', 'error');
            }
        }
    }

    async function forceLogout(phone) {
        if (!confirm(`确定要将手机号为 ${phone} 的用户强制下线吗？`)) {
            return;
        }
        try {
            const data = await fetchWithAuth(`${API_URL}/api/admin/force-logout`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ phone })
            });
            if (data.success) {
                showAdminMessage('用户已被强制下线。', 'success');
                // 状态变化会通过SSE实时更新，无需手动刷新
            } else {
                showAdminMessage(data.message || '强制下线失败。', 'error');
            }
        } catch (error) {
            if (error.message !== 'Unauthorized') {
                showAdminMessage('网络错误，请稍后重试。', 'error');
            }
        }
    }

    function copyCode(code) {
        navigator.clipboard.writeText(code).then(() => {
            showAdminMessage('激活码已复制到剪贴板。', 'success');
        }).catch(() => {
            showAdminMessage('复制失败，请手动复制。', 'error');
        });
    }

    // 平台账号信息提交函数
    async function submitAccountInfo() {
        const jsonDataInput = document.getElementById('account-json-data');
        if (!jsonDataInput) {
            console.error('找不到 account-json-data 元素');
            showAdminMessage('系统错误：找不到输入框', 'error', accountInfoMessage);
            return;
        }

        const jsonData = jsonDataInput.value.trim();

        if (!jsonData) {
            showAdminMessage('请输入平台账号JSON数据', 'error', accountInfoMessage);
            return;
        }

        let accountData;
        try {
            // 解析JSON数据
            accountData = JSON.parse(jsonData);

            // 验证必要字段
            if (!accountData.phone) {
                showAdminMessage('JSON数据中缺少phone字段', 'error', accountInfoMessage);
                return;
            }

            // 确保stats字段存在且为对象
            if (!accountData.stats || typeof accountData.stats !== 'object') {
                showAdminMessage('JSON数据中缺少stats字段或格式不正确', 'error', accountInfoMessage);
                return;
            }

        } catch (error) {
            showAdminMessage('JSON格式错误，请检查输入数据', 'error', accountInfoMessage);
            return;
        }

        try {
            let response;
            if (currentEditingAccountId) {
                // 编辑模式 - 使用正确的主账号ID
                const mainAccountId = window.currentEditingMainAccountId || 1;
                response = await fetchWithAuth(`${API_URL}/api/admin/update-platform-account`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        user_id: mainAccountId,
                        phone: currentEditingAccountId,
                        accountData: accountData,
                        isMainAccount: true
                    })
                });
            } else {
                // 添加模式 - 默认归属给管理员
                response = await fetchWithAuth(`${API_URL}/api/admin/add-platform-account`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        accountData: accountData,
                        ownerId: 1, // 默认归属给管理员
                        currentHolderId: 1 // 默认归属给管理员
                    })
                });
            }

            if (response.success) {
                const successMessage = currentEditingAccountId ? '平台账号信息更新成功' : '平台账号添加成功，收益数据正在获取中...';
                showAdminMessage(successMessage, 'success', accountInfoMessage);
                setTimeout(() => {
                    hideAccountInfoModal();
                    loadAccountInfoList();
                    showAdminMessage(successMessage, 'success');
                }, 1500);
            } else {
                // 统一错误处理：显示服务器返回的具体错误信息
                const errorMessage = response.message || '操作失败，请检查输入数据';
                showAdminMessage(errorMessage, 'error', accountInfoMessage);
                console.error('平台账号操作失败:', response);
            }
        } catch (error) {
            // 统一错误处理：区分网络错误和其他错误
            if (error.message === 'Unauthorized') {
                showAdminMessage('会话已过期，请重新登录', 'error', accountInfoMessage);
            } else {
                showAdminMessage('网络错误，请稍后重试', 'error', accountInfoMessage);
            }
            console.error('提交平台账号信息失败:', error);
        }
    }

    // 编辑平台账号信息
    async function editAccountInfo(platformPhone, mainAccountId) {
        try {
            // 使用新的API获取所有主账号的平台账户信息
            const data = await fetchWithAuth(`${API_URL}/api/admin/platform-accounts?get_all_main_accounts=true`);
            if (data.success) {
                // 查找指定的平台账号信息
                const accountInfo = data.accountInfos.find(info =>
                    info.phone === platformPhone && info.main_account_id === mainAccountId
                );
                if (accountInfo) {
                    // 保存当前编辑的主账号ID，用于后续更新
                    window.currentEditingMainAccountId = mainAccountId;
                    showAccountInfoModal(true, accountInfo);
                } else {
                    showAdminMessage('未找到平台账号信息', 'error');
                }
            }
        } catch (error) {
            showAdminMessage('获取平台账号信息失败', 'error');
            console.error('获取平台账号信息失败:', error);
        }
    }

    // 删除平台账号信息
    async function deleteAccountInfo(platformPhone, mainAccountId) {
        if (!confirm('确定要删除这条平台账号信息吗？此操作不可恢复。')) {
            return;
        }

        try {
            const response = await fetchWithAuth(`${API_URL}/api/admin/platform-accounts`, {
                method: 'DELETE',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    phone: platformPhone,
                    user_id: mainAccountId // 使用主账号ID
                })
            });

            if (response.success) {
                showAdminMessage('平台账号信息删除成功', 'success');
                loadAccountInfoList();
            } else {
                showAdminMessage(response.message || '删除失败', 'error');
            }
        } catch (error) {
            showAdminMessage('网络错误，请稍后重试', 'error');
            console.error('删除平台账号信息失败:', error);
        }
    }

    // 加载转移用户选项
    async function loadTransferUserOptions(mainAccountId) {
        try {
            // 清空现有选项
            transferNewHolderSelect.innerHTML = '<option value="">请选择用户...</option>';

            // 获取主账号信息
            const mainAccountResponse = await fetchWithAuth(`${API_URL}/api/admin/get-accounts`);
            if (mainAccountResponse.success) {
                const mainAccount = mainAccountResponse.accounts.find(acc => acc.id === mainAccountId);
                if (mainAccount) {
                    // 添加主账号选项
                    const mainOption = document.createElement('option');
                    mainOption.value = mainAccount.id;
                    mainOption.textContent = `${mainAccount.phone} (主账号)`;
                    transferNewHolderSelect.appendChild(mainOption);
                }
            }

            // 获取子账号列表
            const subAccountsResponse = await fetchWithAuth(`${API_URL}/api/admin/get-sub-accounts?ownerId=${mainAccountId}`);
            if (subAccountsResponse.success && subAccountsResponse.subAccounts) {
                subAccountsResponse.subAccounts.forEach(subAccount => {
                    const option = document.createElement('option');
                    option.value = subAccount.id;
                    option.textContent = `${subAccount.phone} (子账号)`;
                    transferNewHolderSelect.appendChild(option);
                });
            }
        } catch (error) {
            console.error('加载用户选项失败:', error);
            showAdminMessage('加载用户列表失败', 'error', transferMessage);
        }
    }

    // 显示转移模态框
    async function showTransferModal(platformPhone, mainAccountId) {
        currentTransferAccountId = platformPhone;
        currentTransferMainAccountId = mainAccountId;
        transferNewHolderSelect.value = '';
        transferMessage.classList.add('hidden');

        // 获取主账号和子账号列表
        await loadTransferUserOptions(mainAccountId);

        transferModal.classList.remove('hidden');
    }

    // 隐藏转移模态框
    function hideTransferModal() {
        transferModal.classList.add('hidden');
        currentTransferAccountId = null;
        currentTransferMainAccountId = null;
    }

    // 确认转移平台账号
    async function confirmTransfer() {
        const newHolderId = parseInt(transferNewHolderSelect.value);

        if (!newHolderId || !currentTransferAccountId || !currentTransferMainAccountId) {
            showAdminMessage('请选择有效的用户', 'error', transferMessage);
            return;
        }

        try {
            const response = await fetchWithAuth(`${API_URL}/api/admin/transfer-platform-account`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    phone: currentTransferAccountId,
                    new_holder_id: newHolderId,
                    user_id: currentTransferMainAccountId // 使用主账号ID
                })
            });

            if (response.success) {
                showAdminMessage('平台账号转移成功', 'success', transferMessage);
                setTimeout(() => {
                    hideTransferModal();
                    loadAccountInfoList();
                    showAdminMessage('平台账号转移成功', 'success');
                }, 1500);
            } else {
                showAdminMessage(response.message || '转移失败', 'error', transferMessage);
            }
        } catch (error) {
            showAdminMessage('网络错误，请稍后重试', 'error', transferMessage);
            console.error('转移平台账号失败:', error);
        }
    }

    // 将函数设为全局，以便在HTML中调用
    window.editAccountInfo = editAccountInfo;
    window.deleteAccountInfo = deleteAccountInfo;
    window.showTransferModal = showTransferModal;

    async function refreshAllActiveUsers() {
        if (!confirm('确定要刷新全部账号状态吗？这将重新加载所有用户的状态信息。')) {
            return;
        }
        try {
            showAdminMessage('正在刷新全部账号状态，请稍候...', 'info');
            // 直接刷新账号列表，依赖SSE的实时状态更新
            await loadAccounts();
            showAdminMessage('账号状态刷新完成！', 'success');
        } catch (error) {
            console.error('刷新账号状态失败:', error);
            showAdminMessage('刷新账号状态失败。', 'error');
        }
    }

    // 自动刷新所有用户状态（管理员登录时调用，无确认对话框）
    async function autoRefreshAllActiveUsers() {
        try {
            // 直接加载账号列表，依赖SSE的实时状态更新
            await loadAccounts();
        } catch (error) {
            if (error.message !== 'Unauthorized') {
                console.error('自动刷新失败:', error);
            }
        }
    }



    searchBtn.addEventListener('click', () => {
        loadAccounts(searchInput.value.trim());
    });
    searchInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') searchBtn.click();
    });

    filterCodesBtn.addEventListener('click', () => {
        loadActivationCodes(codeTypeFilter.value, codeStatusFilter.value);
    });

    generateCodesBtn.addEventListener('click', generateActivationCodes);
    refreshAllUsersBtn.addEventListener('click', refreshAllActiveUsers);

    // 账号信息相关事件监听器
    accountInfoSearchBtn.addEventListener('click', () => {
        loadAccountInfoList(accountInfoSearchInput.value.trim());
    });
    accountInfoSearchInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') accountInfoSearchBtn.click();
    });
    if (addAccountInfoBtn) {
        addAccountInfoBtn.addEventListener('click', () => {
            showAccountInfoModal(false);
        });
    }



    // ==================== Durable Objects 批量处理按钮事件 ====================

    // Durable Objects 批量处理按钮
    const durableBatchUpdateBtn = document.getElementById('durable-batch-update-btn');
    if (durableBatchUpdateBtn) {
        durableBatchUpdateBtn.addEventListener('click', async () => {
            await startDurableBatchUpdate();
        });
    }

    // 批量处理状态按钮
    const batchStatusBtn = document.getElementById('batch-status-btn');
    if (batchStatusBtn) {
        batchStatusBtn.addEventListener('click', async () => {
            await getBatchStatus();
        });
    }

    // 取消批量处理按钮
    const cancelBatchBtn = document.getElementById('cancel-batch-btn');
    if (cancelBatchBtn) {
        cancelBatchBtn.addEventListener('click', async () => {
            await cancelBatchUpdate();
        });
    }

    // 重置批量处理状态按钮
    const resetBatchBtn = document.getElementById('reset-batch-btn');
    if (resetBatchBtn) {
        resetBatchBtn.addEventListener('click', async () => {
            if (confirm('确定要重置批量处理状态吗？这将清除所有进度信息。')) {
                await resetBatchState();
            }
        });
    }

    // 页面加载时检查是否有正在进行的批量处理
    window.addEventListener('load', async () => {
        try {
            await getBatchStatus();
        } catch (error) {
            console.log('页面加载时检查批量处理状态失败:', error);
        }
    });

    // 页面卸载时清理资源
    window.addEventListener('beforeunload', () => {
        if (batchEventSource) {
            batchEventSource.close();
        }
        if (batchStatusPollingInterval) {
            clearInterval(batchStatusPollingInterval);
        }
    });

    // 一键更新收益按钮
    const manualUpdateIncomeBtn = document.getElementById('manual-update-income-btn');
    if (manualUpdateIncomeBtn) {
        manualUpdateIncomeBtn.addEventListener('click', async () => {
            await manualUpdateIncomeData();
        });
    }

    // 一键更新信用分和粉丝数按钮
    const manualUpdateCreditFansBtn = document.getElementById('manual-update-credit-fans-btn');
    if (manualUpdateCreditFansBtn) {
        manualUpdateCreditFansBtn.addEventListener('click', async () => {
            await manualUpdateCreditAndFansData();
        });
    }

    // 一键更新账号详细信息按钮
    const manualUpdateAccountInfoBtn = document.getElementById('manual-update-account-info-btn');
    if (manualUpdateAccountInfoBtn) {
        manualUpdateAccountInfoBtn.addEventListener('click', async () => {
            await manualUpdateAccountInfoData();
        });
    }

    // 一键更新账号状态按钮
    const manualUpdateAccountStatusBtn = document.getElementById('manual-update-account-status-btn');
    if (manualUpdateAccountStatusBtn) {
        manualUpdateAccountStatusBtn.addEventListener('click', async () => {
            await manualUpdateAccountStatusData();
        });
    }

    // 一键更新阅读量数据按钮
    const manualUpdateReadingDataBtn = document.getElementById('manual-update-reading-data-btn');
    if (manualUpdateReadingDataBtn) {
        manualUpdateReadingDataBtn.addEventListener('click', async () => {
            await manualUpdateReadingData();
        });
    }

    // 一键更新草稿箱数量按钮
    const manualUpdateDraftsCountBtn = document.getElementById('manual-update-drafts-count-btn');
    if (manualUpdateDraftsCountBtn) {
        manualUpdateDraftsCountBtn.addEventListener('click', async () => {
            await manualUpdateDraftsCount();
        });
    }



    // 确保DOM完全加载后再绑定事件监听器
    document.addEventListener('DOMContentLoaded', function() {
        // 重新获取DOM元素（确保元素已存在）
        const closeBtn = document.querySelector('.close-account-info-modal');
        const submitBtn = document.getElementById('submit-account-info-btn');
        const modal = document.getElementById('account-info-modal');

        if (closeBtn) {
            closeBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                hideAccountInfoModal();
            });
        }

        if (submitBtn) {
            submitBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                submitAccountInfo();
            });
        }
    });

    // 立即绑定（兼容性处理）
    if (closeAccountInfoModalBtn) {
        closeAccountInfoModalBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            hideAccountInfoModal();
        });
    }

    if (submitAccountInfoBtn) {
        submitAccountInfoBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            submitAccountInfo();
        });
    }

    // 转移相关事件监听器
    closeTransferModalBtn.addEventListener('click', hideTransferModal);
    confirmTransferBtn.addEventListener('click', confirmTransfer);

    // 定时任务测试相关事件监听器
    const testScheduledTaskBtn = document.getElementById('test-scheduled-task-btn');
    if (testScheduledTaskBtn) {
        testScheduledTaskBtn.addEventListener('click', showScheduledTaskModal);
    }

    // 测试重置昨日数据按钮
    const testResetBtn = document.getElementById('test-reset-btn');
    if (testResetBtn) {
        testResetBtn.addEventListener('click', testResetYesterdayData);
    }

    // 测试重置昨日数据功能
    async function testResetYesterdayData() {
        if (!confirm('确定要手动执行昨日数据重置任务吗？这将清零所有账号的昨日收益和昨日阅读数据。')) {
            return;
        }

        try {
            showAdminMessage('正在执行昨日数据重置任务...', 'info');
            const response = await fetchWithAuth(`${API_URL}/api/admin/test-reset-yesterday-data`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });

            if (response.success) {
                showAdminMessage(`重置任务执行成功！共处理 ${response.affectedAccounts} 个平台账号`, 'success');
                // 刷新平台账号信息列表
                loadAccountInfoList();
            } else {
                showAdminMessage(response.message || '重置任务执行失败', 'error');
            }
        } catch (error) {
            showAdminMessage('网络错误，请稍后重试', 'error');
            console.error('重置任务执行失败:', error);
        }
    }

    if (closeScheduledTaskModalBtn) {
        closeScheduledTaskModalBtn.addEventListener('click', hideScheduledTaskModal);
    }

    if (startScheduledTestBtn) {
        startScheduledTestBtn.addEventListener('click', startScheduledTask);
    }

    if (stopScheduledTestBtn) {
        stopScheduledTestBtn.addEventListener('click', stopScheduledTask);
    }

    if (clearLogsBtn) {
        clearLogsBtn.addEventListener('click', clearTaskLogs);
    }

    // API压力测试相关事件监听器
    const testApiPressureBtn = document.getElementById('test-api-pressure-btn');
    if (testApiPressureBtn) {
        testApiPressureBtn.addEventListener('click', showApiPressureModal);
    }

    if (closeApiPressureModalBtn) {
        closeApiPressureModalBtn.addEventListener('click', hideApiPressureModal);
    }

    if (startPressureTestBtn) {
        startPressureTestBtn.addEventListener('click', startPressureTest);
    }

    if (stopPressureTestBtn) {
        stopPressureTestBtn.addEventListener('click', stopPressureTest);
    }

    if (clearPressureLogsBtn) {
        clearPressureLogsBtn.addEventListener('click', clearPressureTestLogs);
    }

    // 点击模态框外部关闭
    window.addEventListener('click', (event) => {
        if (event.target === accountInfoModal) {
            hideAccountInfoModal();
        }
        if (event.target === transferModal) {
            hideTransferModal();
        }
        if (event.target === scheduledTaskModal) {
            hideScheduledTaskModal();
        }
        if (event.target === apiPressureModal) {
            hideApiPressureModal();
        }
    });

    // 键盘事件支持 - ESC键关闭模态框
    document.addEventListener('keydown', (event) => {
        if (event.key === 'Escape') {
            const modal = document.getElementById('account-info-modal');
            const transferModalEl = document.getElementById('transfer-modal');
            const scheduledTaskModalEl = document.getElementById('scheduled-task-modal');

            if (modal && !modal.classList.contains('hidden')) {
                hideAccountInfoModal();
                event.preventDefault();
                event.stopPropagation();
            }
            if (transferModalEl && !transferModalEl.classList.contains('hidden')) {
                hideTransferModal();
                event.preventDefault();
                event.stopPropagation();
            }
            if (scheduledTaskModalEl && !scheduledTaskModalEl.classList.contains('hidden')) {
                hideScheduledTaskModal();
                event.preventDefault();
                event.stopPropagation();
            }

            const apiPressureModalEl = document.getElementById('api-pressure-modal');
            if (apiPressureModalEl && !apiPressureModalEl.classList.contains('hidden')) {
                hideApiPressureModal();
                event.preventDefault();
                event.stopPropagation();
            }
        }
    });

    // 修改密码对话框相关功能
    changePasswordBtn.addEventListener('click', () => {
      // 打开修改密码对话框
      changePasswordModal.classList.remove('hidden');
      // 清空输入框和消息
      oldPasswordInput.value = '';
      newPasswordInput.value = '';
      confirmPasswordInput.value = '';
      passwordChangeMessage.classList.add('hidden');
    });

    closeModalBtn.addEventListener('click', () => {
      // 关闭修改密码对话框
      changePasswordModal.classList.add('hidden');
    });

    // 点击对话框外部关闭对话框
    window.addEventListener('click', (event) => {
      if (event.target === changePasswordModal) {
        changePasswordModal.classList.add('hidden');
      }
    });

    submitPasswordBtn.addEventListener('click', async () => {
      const oldPassword = oldPasswordInput.value.trim();
      const newPassword = newPasswordInput.value.trim();
      const confirmPassword = confirmPasswordInput.value.trim();

      // 验证输入
      if (!oldPassword || !newPassword || !confirmPassword) {
        showAdminMessage('请填写所有密码字段', 'error', passwordChangeMessage);
        return;
      }

      if (newPassword !== confirmPassword) {
        showAdminMessage('两次输入的新密码不一致', 'error', passwordChangeMessage);
        return;
      }

      try {
        const response = await fetch(`${API_URL}/api/admin/change-password`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ oldPassword, newPassword })
        });
        const data = await response.json();

        if (data.success) {
          showAdminMessage('密码修改成功！', 'success', passwordChangeMessage);
          // 3秒后关闭对话框
          setTimeout(() => {
            changePasswordModal.classList.add('hidden');
          }, 3000);
        } else {
          showAdminMessage(data.message || '密码修改失败', 'error', passwordChangeMessage);
        }
      } catch (error) {
        console.error('修改密码请求失败:', error);
        showAdminMessage('修改密码请求失败，请稍后重试', 'error', passwordChangeMessage);
      }
    });

    // 子账号管理事件监听器
    // TODO: 子账号创建功能尚未完成，暂时注释掉相关代码
    // cancelSubAccountBtn.addEventListener('click', hideCreateSubAccountModal); // TODO: 需要定义 cancelSubAccountBtn
    // createSubAccountBtn.addEventListener('click', createSubAccount); // TODO: 需要定义 createSubAccountBtn

    // 点击模态框外部关闭子账号创建对话框
    // TODO: createSubAccountModal 和 hideCreateSubAccountModal 尚未定义，暂时注释掉
    /*
    window.addEventListener('click', (event) => {
      if (event.target === createSubAccountModal) {
        hideCreateSubAccountModal();
      }
    });
    */

    // 子账号表单回车键提交
    // TODO: subAccountConfirmPasswordInput 和 createSubAccount 尚未定义，暂时注释掉
    /*
    subAccountConfirmPasswordInput.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        createSubAccount();
      }
    });
    */

    // 加载客户端版本
    async function loadClientVersion() {
      try {
        const response = await fetch(`${API_URL}/api/admin/get-client-version`, {
          method: 'GET',
          headers: { 'Content-Type': 'application/json' }
        });
        const data = await response.json();
        if (data.success) {
          currentVersionDisplay.value = data.version;
        } else {
          showAdminMessage('获取版本信息失败: ' + data.message, 'error');
        }
      } catch (error) {
        showAdminMessage('获取版本信息请求失败', 'error');
        console.error('获取版本信息失败:', error);
      }
    }

    // 设置客户端版本
    setVersionBtn.addEventListener('click', async () => {
      const newVersion = newVersionInput.value.trim();
      if (!newVersion) {
        showAdminMessage('请输入新版本号', 'error');
        return;
      }
      
      try {
        const data = await fetchWithAuth(`${API_URL}/api/admin/set-client-version`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ version: newVersion })
        });
        if (data.success) {
          showAdminMessage('版本更新成功', 'success');
          currentVersionDisplay.value = newVersion;
          newVersionInput.value = '';
        } else {
          showAdminMessage('版本更新失败: ' + data.message, 'error');
        }
      } catch (error) {
        showAdminMessage('版本更新请求失败', 'error');
        console.error('版本更新失败:', error);
      }
    });

    // Initial load for default tab (if admin is already logged in, not handled here)
    // checkLoginStatus will show login first.
  </script>
</body>
</html>