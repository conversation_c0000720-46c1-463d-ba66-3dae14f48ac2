import { DurableObject } from 'cloudflare:workers';
import type { D1Database, DurableObjectState } from '@cloudflare/workers-types';
import { allDataFetcher, BatchProcessor } from './dataFetcher-new';
import { FetchResult, BatchOperationResult } from './types';

// 批量处理进度状态
export interface BatchProgress {
  batchId: string;
  totalAccounts: number;
  processedAccounts: number;
  successCount: number;
  failureCount: number;
  currentBatch: number;
  totalBatches: number;
  status: 'waiting' | 'processing' | 'completed' | 'failed' | 'cancelled';
  startTime: number;
  lastUpdateTime: number;
  currentAccount?: string;
  results: FetchResult[];
  errorMessage?: string;
}

// 批量处理配置
export interface BatchConfig {
  batchSize: number; // 每批处理的账号数量
  delayBetweenBatches: number; // 批次间延迟（毫秒）
  delayBetweenAccounts: number; // 账号间延迟（毫秒）
}

// SSE 连接管理
interface SSEConnection {
  webSocket: WebSocket;
  batchId: string;
  connectedAt: number;
}

/**
 * 批量处理器 Durable Object
 * 使用 Alarms 实现分批次顺序处理，通过 SSE 推送进度
 */
export class BatchProcessorDO extends DurableObject {
  private state: DurableObjectState;
  private db: D1Database;
  private env: any;
  private currentProgress: BatchProgress | null = null;
  private accounts: Array<{ mainAccountId: number; phone: string; sessionid: string }> = [];
  private config: BatchConfig = {
    batchSize: 4,
    delayBetweenBatches: 500, // 0.5秒
    delayBetweenAccounts: 100  // 0.1秒
  };

  constructor(state: DurableObjectState, env: any) {
    super(state, env);
    this.state = state;
    this.db = env.DB;
    this.env = env;

    // 初始化时检查是否有未完成的批处理
    this.state.blockConcurrencyWhile(async () => {
      const storedProgress = await this.state.storage.get<BatchProgress>('currentProgress');
      if (storedProgress && storedProgress.status === 'processing') {
        this.currentProgress = storedProgress;
        this.accounts = await this.state.storage.get('accounts') || [];
        console.log(`恢复未完成的批处理: ${storedProgress.batchId}`);
      }
    });
  }

  async fetch(request: Request): Promise<Response> {
    const url = new URL(request.url);
    const action = url.pathname.split('/').pop();

    switch (action) {
      case 'start-batch':
        return this.handleStartBatch(request);
      
      case 'progress':
        return this.handleSSEConnection(request);
      
      case 'status':
        return this.handleGetStatus();
      
      case 'cancel':
        return this.handleCancelBatch();

      case 'reset':
        return this.handleResetBatch();

      default:
        return new Response('Not Found', { status: 404 });
    }
  }

  /**
   * 启动批量处理
   */
  private async handleStartBatch(request: Request): Promise<Response> {
    try {
      const { config } = await request.json<{ config?: Partial<BatchConfig> }>();
      
      // 检查是否已有正在进行的批处理
      if (this.currentProgress && this.currentProgress.status === 'processing') {
        return new Response(JSON.stringify({
          success: false,
          message: '已有批处理正在进行中',
          batchId: this.currentProgress.batchId
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      }

      // 更新配置
      if (config) {
        this.config = { ...this.config, ...config };
      }

      // 获取所有需要处理的账号
      const processor = new BatchProcessor(this.db);
      this.accounts = await processor.getAllAccounts();

      if (this.accounts.length === 0) {
        return new Response(JSON.stringify({
          success: false,
          message: '没有找到需要处理的账号'
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      }

      // 创建批处理进度
      const batchId = `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const totalBatches = Math.ceil(this.accounts.length / this.config.batchSize);
      
      this.currentProgress = {
        batchId,
        totalAccounts: this.accounts.length,
        processedAccounts: 0,
        successCount: 0,
        failureCount: 0,
        currentBatch: 0,
        totalBatches,
        status: 'processing',
        startTime: Date.now(),
        lastUpdateTime: Date.now(),
        results: []
      };

      // 保存状态
      await this.state.storage.put('currentProgress', this.currentProgress);
      await this.state.storage.put('accounts', this.accounts);

      // 设置第一个 alarm 立即开始处理
      await this.state.storage.setAlarm(Date.now() + 100);

      console.log(`🚀 [DO+Alarms] 批量处理已启动: ${batchId}, 总账号数: ${this.accounts.length}, 分${totalBatches}批处理`);
      console.log(`⏰ [DO+Alarms] 第一个 Alarm 已设置，100ms后开始处理`);

      return new Response(JSON.stringify({
        success: true,
        message: '批量处理已启动',
        batchId,
        totalAccounts: this.accounts.length,
        totalBatches
      }), {
        headers: { 'Content-Type': 'application/json' }
      });

    } catch (error) {
      console.error('启动批量处理失败:', error);
      return new Response(JSON.stringify({
        success: false,
        message: `启动失败: ${error}`
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
  }

  /**
   * 通过管理员 SSE 推送批量处理进度
   */
  private async pushProgressToAdminSSE(progress: BatchProgress): Promise<void> {
    try {
      // 获取 UserAuthDO 实例
      const id = this.env.USER_AUTH_DO.idFromName("auth");
      const userAuthDO = this.env.USER_AUTH_DO.get(id);

      // 构造进度消息
      const progressMessage = {
        type: 'batch_progress',
        data: progress,
        timestamp: new Date().toISOString()
      };

      // 通过 UserAuthDO 推送到管理员 SSE 连接
      const request = new Request('https://dummy.com/admin/push-batch-progress', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(progressMessage)
      });

      await userAuthDO.fetch(request);
      console.log(`📡 [DO+Alarms] 进度已推送到管理员 SSE: ${progress.processedAccounts}/${progress.totalAccounts}`);
    } catch (error) {
      console.error('推送进度到管理员 SSE 失败:', error);
    }
  }

  /**
   * 处理 SSE 连接（已废弃，使用管理员 SSE 代替）
   */
  private async handleSSEConnection(request: Request): Promise<Response> {
    const url = new URL(request.url);
    const batchId = url.searchParams.get('batchId');

    if (!batchId) {
      return new Response('Missing batchId parameter', { status: 400 });
    }

    // 创建一个简单的 SSE 流，返回当前状态
    let responseText = '';

    // 发送连接确认
    responseText += 'data: {"type":"connected","message":"SSE连接已建立"}\n\n';

    // 如果有当前进度，立即发送
    if (this.currentProgress && this.currentProgress.batchId === batchId) {
      const progressData = JSON.stringify({
        type: 'progress',
        data: this.currentProgress
      });
      responseText += `data: ${progressData}\n\n`;
    } else {
      // 发送空状态
      responseText += 'data: {"type":"progress","data":null}\n\n';
    }

    return new Response(responseText, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control'
      }
    });
  }

  /**
   * 获取当前状态
   */
  private async handleGetStatus(): Promise<Response> {
    return new Response(JSON.stringify({
      success: true,
      data: this.currentProgress
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
  }

  /**
   * 取消批量处理
   */
  private async handleCancelBatch(): Promise<Response> {
    if (this.currentProgress && this.currentProgress.status === 'processing') {
      this.currentProgress.status = 'cancelled';
      this.currentProgress.lastUpdateTime = Date.now();

      await this.state.storage.put('currentProgress', this.currentProgress);
      await this.state.storage.deleteAlarm();

      console.log(`批量处理已取消: ${this.currentProgress.batchId}`);

      return new Response(JSON.stringify({
        success: true,
        message: '批量处理已取消'
      }), {
        headers: { 'Content-Type': 'application/json' }
      });
    }

    return new Response(JSON.stringify({
      success: false,
      message: '没有正在进行的批量处理'
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
  }

  /**
   * 强制重置批量处理状态
   */
  private async handleResetBatch(): Promise<Response> {
    try {
      console.log('🔄 强制重置批量处理状态...');

      // 清除所有状态
      this.currentProgress = null;
      this.accounts = [];

      // 删除存储的状态
      await this.state.storage.delete('currentProgress');
      await this.state.storage.delete('accounts');

      // 删除任何设置的 alarm
      await this.state.storage.deleteAlarm();

      console.log('✅ 批量处理状态已重置');

      return new Response(JSON.stringify({
        success: true,
        message: '批量处理状态已重置'
      }), {
        headers: { 'Content-Type': 'application/json' }
      });
    } catch (error) {
      console.error('重置批量处理状态失败:', error);
      return new Response(JSON.stringify({
        success: false,
        message: `重置失败: ${error}`
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
  }

  /**
   * Alarm 处理器 - 处理当前批次
   */
  async alarm(): Promise<void> {
    console.log('🔔 Durable Objects Alarm 被触发！');

    if (!this.currentProgress || this.currentProgress.status !== 'processing') {
      console.log('❌ 没有需要处理的批次或状态不正确');
      return;
    }

    try {
      console.log(`🚀 [DO+Alarms] 开始处理第 ${this.currentProgress.currentBatch + 1}/${this.currentProgress.totalBatches} 批`);

      // 计算当前批次的账号范围
      const startIndex = this.currentProgress.currentBatch * this.config.batchSize;
      const endIndex = Math.min(startIndex + this.config.batchSize, this.accounts.length);
      const currentBatchAccounts = this.accounts.slice(startIndex, endIndex);

      console.log(`处理账号 ${startIndex + 1}-${endIndex}/${this.accounts.length}`);

      // 处理当前批次的账号
      for (let i = 0; i < currentBatchAccounts.length; i++) {
        const account = currentBatchAccounts[i];
        
        if (this.currentProgress.status !== 'processing') {
          console.log('批处理已被取消，停止处理');
          return;
        }

        this.currentProgress.currentAccount = account.phone;
        this.currentProgress.lastUpdateTime = Date.now();

        console.log(`📱 [DO+Alarms] 处理账号 ${account.phone} (${this.currentProgress.processedAccounts + 1}/${this.currentProgress.totalAccounts}) - 获取全部信息`);

        try {
          // 获取账号的全部数据（收益、信用分、粉丝数、账号信息、状态、阅读量、草稿箱）
          const fetchResult = await allDataFetcher(account.phone, account.sessionid);
          this.currentProgress.results.push(fetchResult);

          if (fetchResult.success && fetchResult.data) {
            // 更新数据库
            const processor = new BatchProcessor(this.db);
            const updateResult = await processor.updateDatabase(account, fetchResult.data);
            
            if (updateResult.success) {
              this.currentProgress.successCount++;
              console.log(`✅ [DO+Alarms] 账号 ${account.phone} 全部信息处理成功`);
            } else {
              this.currentProgress.failureCount++;
              console.log(`❌ [DO+Alarms] 账号 ${account.phone} 数据库更新失败: ${updateResult.message}`);
            }
          } else {
            this.currentProgress.failureCount++;
            console.log(`❌ [DO+Alarms] 账号 ${account.phone} 数据获取失败: ${fetchResult.message}`);
          }

        } catch (error) {
          this.currentProgress.failureCount++;
          console.error(`处理账号 ${account.phone} 时发生异常:`, error);
          
          this.currentProgress.results.push({
            success: false,
            phone: account.phone,
            message: `处理异常: ${error}`
          });
        }

        this.currentProgress.processedAccounts++;
        this.currentProgress.lastUpdateTime = Date.now();

        // 保存进度
        await this.state.storage.put('currentProgress', this.currentProgress);

        // 推送进度到管理员 SSE
        await this.pushProgressToAdminSSE(this.currentProgress);

        // 账号间延迟
        if (i < currentBatchAccounts.length - 1) {
          await new Promise(resolve => setTimeout(resolve, this.config.delayBetweenAccounts));
        }
      }

      // 更新批次计数
      this.currentProgress.currentBatch++;
      this.currentProgress.currentAccount = undefined;

      // 检查是否还有更多批次需要处理
      if (this.currentProgress.currentBatch < this.currentProgress.totalBatches) {
        // 设置下一个批次的 alarm
        await this.state.storage.setAlarm(Date.now() + this.config.delayBetweenBatches);
        console.log(`⏰ [DO+Alarms] 第 ${this.currentProgress.currentBatch}/${this.currentProgress.totalBatches} 批处理完成，${this.config.delayBetweenBatches}ms后处理下一批`);
      } else {
        // 所有批次处理完成
        this.currentProgress.status = 'completed';
        this.currentProgress.lastUpdateTime = Date.now();
        console.log(`🎉 [DO+Alarms] 所有批次处理完成！成功: ${this.currentProgress.successCount}, 失败: ${this.currentProgress.failureCount}`);
      }

      // 保存最终状态
      await this.state.storage.put('currentProgress', this.currentProgress);

      // 推送最终进度到管理员 SSE
      await this.pushProgressToAdminSSE(this.currentProgress);

    } catch (error) {
      console.error('批次处理失败:', error);
      
      if (this.currentProgress) {
        this.currentProgress.status = 'failed';
        this.currentProgress.errorMessage = String(error);
        this.currentProgress.lastUpdateTime = Date.now();
        await this.state.storage.put('currentProgress', this.currentProgress);
      }
    }
  }
}
