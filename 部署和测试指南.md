# Durable Objects 批量处理 - 部署和测试指南

## 🚀 快速部署

### 1. 部署到 Cloudflare Workers

```bash
# 部署应用
npx wrangler deploy

# 查看部署状态
npx wrangler tail
```

### 2. 验证 Durable Objects 配置

部署成功后，检查以下内容：

- ✅ `BatchProcessorDO` 已注册
- ✅ `BATCH_PROCESSOR_DO` 绑定可用
- ✅ 数据库迁移完成

## 🧪 测试步骤

### 方法一：使用测试页面

1. **打开测试页面**
   ```
   https://your-domain.com/batch-test.html
   ```

2. **配置参数**
   - 每批账号数量：4（推荐）
   - 批次间延迟：500ms
   - 账号间延迟：100ms

3. **获取管理员Token**
   ```bash
   # 如果还没有管理员账号，先创建
   curl -X POST "https://your-domain.com/api/admin/login" \
     -H "Content-Type: application/json" \
     -d '{"username":"admin","password":"your_password"}'
   ```

4. **修改测试页面中的Token**
   在 `batch-test.html` 中找到 `YOUR_ADMIN_TOKEN` 并替换为实际token

5. **开始测试**
   - 点击"启动批量处理"
   - 观察实时进度更新
   - 查看详细日志

### 方法二：使用 API 直接测试

1. **启动批量处理**
   ```bash
   curl -X POST "https://your-domain.com/api/admin/start-durable-batch-update" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
     -d '{
       "config": {
         "batchSize": 4,
         "delayBetweenBatches": 500,
         "delayBetweenAccounts": 100
       }
     }'
   ```

2. **获取处理状态**
   ```bash
   curl -X GET "https://your-domain.com/api/admin/batch-status" \
     -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
   ```

3. **监听 SSE 进度**
   ```bash
   curl -N -H "Accept: text/event-stream" \
     "https://your-domain.com/api/admin/batch-progress-sse?batchId=BATCH_ID&token=YOUR_ADMIN_TOKEN"
   ```

4. **取消处理（如需要）**
   ```bash
   curl -X POST "https://your-domain.com/api/admin/cancel-batch-update" \
     -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
   ```

## 📊 测试验证点

### 功能验证

- [ ] **启动成功**：返回 batchId 和账号总数
- [ ] **进度推送**：SSE 连接正常，进度实时更新
- [ ] **批次处理**：按配置的批次大小和延迟执行
- [ ] **数据更新**：账号数据正确更新到数据库
- [ ] **状态管理**：可以正确获取当前处理状态
- [ ] **错误处理**：失败账号有详细错误信息
- [ ] **完成通知**：处理完成后状态正确更新

### 性能验证

- [ ] **延迟控制**：批次间延迟符合配置
- [ ] **内存使用**：Durable Object 内存使用稳定
- [ ] **处理速度**：单个账号处理时间合理
- [ ] **并发限制**：不会触发 API 限制

### 可靠性验证

- [ ] **状态持久化**：重启后能恢复处理状态
- [ ] **错误恢复**：单个账号失败不影响整体处理
- [ ] **取消功能**：可以正确取消正在进行的处理
- [ ] **重复启动**：正在处理时不能重复启动

## 🔍 监控和调试

### 查看日志

```bash
# 实时查看 Worker 日志
npx wrangler tail

# 查看特定时间段的日志
npx wrangler tail --since 2024-01-01T00:00:00Z
```

### 关键日志信息

- `批量处理已启动: batch_xxx` - 处理开始
- `开始处理第 X/Y 批` - 批次开始
- `处理账号 phone (X/Y)` - 单个账号处理
- `账号 phone 处理成功/失败` - 处理结果
- `所有批次处理完成` - 全部完成

### 常见问题排查

1. **启动失败**
   ```
   错误：已有批处理正在进行中
   解决：等待当前处理完成或调用取消API
   ```

2. **SSE 连接断开**
   ```
   错误：SSE 连接错误
   解决：检查 batchId 和 token，刷新页面重连
   ```

3. **处理停滞**
   ```
   错误：进度长时间不更新
   解决：检查 Alarm 状态，可能需要重启 DO
   ```

4. **账号处理失败**
   ```
   错误：数据获取失败
   解决：检查 sessionid 有效性和 API 限制
   ```

## 📈 性能优化建议

### 批处理配置

- **小规模测试**（<50个账号）：
  - batchSize: 2-4
  - delayBetweenBatches: 300-500ms
  - delayBetweenAccounts: 100ms

- **中等规模**（50-200个账号）：
  - batchSize: 4-6
  - delayBetweenBatches: 500-800ms
  - delayBetweenAccounts: 100-200ms

- **大规模**（>200个账号）：
  - batchSize: 6-8
  - delayBetweenBatches: 800-1000ms
  - delayBetweenAccounts: 200-300ms

### 监控指标

- **处理速度**：账号/分钟
- **成功率**：成功账号数/总账号数
- **平均延迟**：单个账号处理时间
- **错误率**：失败账号数/总账号数

## 🔧 故障恢复

### 手动恢复步骤

1. **检查当前状态**
   ```bash
   curl -X GET "https://your-domain.com/api/admin/batch-status" \
     -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
   ```

2. **如果状态异常，取消当前处理**
   ```bash
   curl -X POST "https://your-domain.com/api/admin/cancel-batch-update" \
     -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
   ```

3. **等待几秒后重新启动**
   ```bash
   # 等待 5 秒
   sleep 5
   
   # 重新启动批量处理
   curl -X POST "https://your-domain.com/api/admin/start-durable-batch-update" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
     -d '{"config":{"batchSize":4,"delayBetweenBatches":500,"delayBetweenAccounts":100}}'
   ```

### 紧急处理

如果 Durable Object 完全无响应：

1. **重新部署应用**
   ```bash
   npx wrangler deploy --force
   ```

2. **等待 DO 重新初始化**（约1-2分钟）

3. **重新启动批量处理**

## ✅ 部署检查清单

- [ ] 代码部署成功
- [ ] Durable Objects 配置正确
- [ ] 数据库连接正常
- [ ] 管理员权限配置
- [ ] 测试页面可访问
- [ ] API 接口响应正常
- [ ] SSE 连接建立成功
- [ ] 批量处理功能正常
- [ ] 日志监控配置
- [ ] 错误处理验证

完成以上检查后，您的 Durable Objects 批量处理系统就可以正式投入使用了！
